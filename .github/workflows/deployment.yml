name: CI/CD for RECRE8 Application

on:
  push:
    branches:
      - market-pulse-menu
      - inventory-scenario-backend-integration

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_FE }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_FE }}
          aws-region: ap-south-1

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies and build
        run: |
          npm install --legacy-peer-deps
          npm run build

      - name: Deploy Static Site to S3 Bucket
        run: aws s3 cp dist/ s3://recre8-fe --recursive

      - name: Invalidate CloudFront cache
        run: |
          aws cloudfront create-invalidation \
            --distribution-id E1F968WUHEKOR2 \
            --paths "/*"
