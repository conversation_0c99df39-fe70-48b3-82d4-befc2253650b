# Duplicate Name Handling for Flow Diagrams

## Overview

This document describes the implementation of duplicate name handling for both inventory and scenario flow diagrams. When a user tries to save a flow diagram with a name that already exists, the system now asks for confirmation before overwriting the existing diagram.

## Implementation Details

### New Components

1. **OverwriteConfirmDialog** (`src/components/OverwriteConfirmDialog.tsx`)
   - A confirmation dialog that appears when a duplicate name is detected
   - Shows a warning message with the flow name and type (inventory/scenario)
   - Provides "Cancel" and "Overwrite" buttons
   - Uses AlertTriangle icon to indicate the destructive nature of the action

### New API Functions

2. **updateFlowDiagramByName** (`src/services/flowDiagramApi.ts`)
   - Uses the name-based PUT endpoint: `/{user_uuid}/{sector_uuid}/{name}/{flow_type}`
   - Updates an existing flow diagram by name instead of UUID
   - Handles validation errors and provides detailed error logging

3. **saveOrUpdateFlowDiagram** (`src/services/flowDiagramApi.ts`)
   - Main function that handles the save/update logic
   - First attempts to create a new flow diagram (POST)
   - If creation fails due to duplicate name, asks for user confirmation
   - If confirmed, uses the PUT endpoint to update the existing diagram
   - Returns null if user cancels the overwrite

### Updated Functions

4. **IndustryFlow.tsx Updates**
   - Added state variables for dialog management:
     - `showOverwriteDialog`: Controls dialog visibility
     - `pendingSaveData`: Stores data for pending save operation
     - `pendingSaveResolver`: Promise resolver for confirmation handling
   
   - Added confirmation handler functions:
     - `handleOverwriteConfirmation()`: Returns a Promise that resolves when user makes a choice
     - `handleOverwriteConfirm()`: Resolves promise with true (proceed with overwrite)
     - `handleOverwriteCancel()`: Resolves promise with false (cancel operation)

   - Updated save functions:
     - `saveFlowToDatabase()`: Now uses `saveOrUpdateFlowDiagram` instead of `createFlowDiagram`
     - `saveScenarioToDatabase()`: Now uses `saveOrUpdateFlowDiagram` instead of `createFlowDiagram`
     - Both functions now handle duplicate name detection and user confirmation

## User Experience Flow

1. **User saves a flow diagram** with an existing name
2. **System attempts POST** to create new diagram
3. **API returns error** indicating duplicate name
4. **Confirmation dialog appears** asking if user wants to overwrite
5. **User chooses**:
   - **Cancel**: Operation is cancelled, no changes made
   - **Overwrite**: System calls PUT endpoint to update existing diagram
6. **Success/Error feedback** is shown via toast notifications

## Error Handling

- **Duplicate name detection**: Checks for common error patterns in API responses
- **Validation errors**: Detailed logging of 422 validation errors
- **Network errors**: Graceful handling of connection issues
- **User cancellation**: Clean state management when user cancels operation

## API Endpoints Used

- **POST** `/flow-diagrams` - Create new flow diagram
- **PUT** `/flow-diagrams/{user_uuid}/{sector_uuid}/{name}/{flow_type}` - Update by name
- **GET** `/flow-diagrams` - List existing flow diagrams (for duplicate checking)

## Benefits

1. **Prevents accidental overwrites** - User must explicitly confirm
2. **Clear warning messages** - Users understand the consequences
3. **Consistent behavior** - Works for both inventory and scenario modes
4. **Graceful error handling** - Proper feedback for all error conditions
5. **State management** - Local state is properly updated after operations

## Testing

To test the functionality:

1. Create and save a flow diagram with a specific name
2. Create another flow diagram and try to save it with the same name
3. Verify that the confirmation dialog appears
4. Test both "Cancel" and "Overwrite" options
5. Verify that the local state is properly updated in both cases

## Future Enhancements

- Add option to save with a different name instead of overwriting
- Show preview of existing diagram before overwriting
- Add bulk operations for managing multiple diagrams
- Implement versioning system for flow diagrams
