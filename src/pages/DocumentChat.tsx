import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@/components/ThemeProvider';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import DocumentViewer from '@/components/DocumentViewer';
import DocumentChatInterface from '@/components/DocumentChatInterface';
import DocumentChatSidebar, { Document } from '@/components/DocumentChatSidebar';
import { SaveAll } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import { createNote } from '@/services/notesApi';
import { NoteType } from '@/types/Note';
import { API_BASE_URL } from '@/utils/endPoints';

const DocumentChat = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [documentUrl, setDocumentUrl] = useState<string | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null);
  const [chatContent, setChatContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [isLoadingDocuments, setIsLoadingDocuments] = useState<boolean>(true);

  // Fetch documents on component mount
  useEffect(() => {
    fetchDocuments();
  }, []);

  // Fetch documents
  const fetchDocuments = async () => {
    setIsLoadingDocuments(true);
    try {
      const accessToken = localStorage.getItem('accessToken');
      if (!accessToken) {
        toast({
          title: "Authentication Required",
          description: "Please log in to view your documents",
          variant: "destructive",
        });
        navigate('/login');
        return;
      }

      const response = await fetch(API_BASE_URL + '/documents', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch documents');
      }

      const data = await response.json();
      setDocuments(data);
    } catch (error) {
      console.error('Error fetching documents:', error);
      toast({
        title: "Error",
        description: "Failed to fetch documents. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingDocuments(false);
    }
  };

  const handleFileUpload = async (file: File) => {
    setIsUploading(true);
    try {
      const accessToken = localStorage.getItem('accessToken');
      if (!accessToken) {
        toast({
          title: "Authentication Required",
          description: "Please log in to upload documents",
          variant: "destructive",
        });
        navigate('/login');
        return;
      }

      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(API_BASE_URL + '/documents', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error('Failed to upload document');
      }

      const data = await response.json();
      
      // Update the documents list and select the newly uploaded document
      setDocuments(prev => [data, ...prev]);
      setSelectedDocumentId(data.uuid);
      setSelectedFile(data.filename);
      setDocumentUrl(data.s3_url);
      
      toast({
        title: "File uploaded",
        description: `${file.name} has been uploaded successfully.`,
        variant: "default",
      });
      
    } catch (error) {
      console.error('Error uploading document:', error);
      toast({
        title: "Upload Failed",
        description: "Failed to upload document. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleDocumentSelect = (id: string) => {
    setSelectedDocumentId(id);
    const selectedDoc = documents.find(doc => doc.uuid === id);
    if (selectedDoc) {
      setSelectedFile(selectedDoc.filename);
      setDocumentUrl(selectedDoc.s3_url);
    }
  };

  const handleNewSession = () => {
    setSelectedDocumentId(null);
    setSelectedFile(null);
    setDocumentUrl(null);
    toast({
      title: "New session created",
      description: "You can now upload a new document.",
      variant: "default",
    });
  };

  const handleChatContentUpdate = (content: string) => {
    setChatContent(content);
  };

  const handleSaveToNotes = async () => {
    if (!chatContent || !selectedDocumentId) {
      toast({
        title: "Cannot Save Note",
        description: "There is no content to save or no document is selected",
        variant: "destructive",
      });
      return;
    }
    
    try {
      // Create a title based on the document name
      const selectedDoc = documents.find(doc => doc.uuid === selectedDocumentId);
      const title = selectedDoc ? `Notes from ${selectedDoc.filename}` : `Document Chat - ${new Date().toLocaleString()}`;
      
      // Create note data
      const noteData = {
        title,
        content: chatContent,
        note_type: NoteType.DOCUMENT,
        is_pinned: false,
        sector_uuid: null
      };
      
      // Save note using the API - pass the useToast hook result directly
      const toastContext = { toast, dismiss: () => {}, toasts: [] };
      const savedNote = await createNote(noteData, toastContext);
      
      if (savedNote) {
        toast({
          title: "Saved to Notes",
          description: "Chat content has been saved to Notes Creator",
        });
        
        // Navigate to the Notes Creator page
        navigate('/notes-creator');
      }
    } catch (error) {
      console.error('Error saving to notes:', error);
      toast({
        title: "Error",
        description: "Failed to save to notes. Please try again later.",
        variant: "destructive",
      });
    }
  };

  const handleDocumentDeleted = (documentId: string) => {
    // Remove the document from the list
    setDocuments(prev => prev.filter(doc => doc.uuid !== documentId));
    
    // If the deleted document was selected, clear the selection
    if (selectedDocumentId === documentId) {
      setSelectedDocumentId(null);
      setSelectedFile(null);
      setDocumentUrl(null);
    }
  };

  return (
    <main className="flex-1 w-full px-1 pb-6">
      <div className="flex flex-col h-[calc(100vh-180px)] rounded-xl border border-border shadow-sm overflow-hidden bg-card/50 backdrop-blur-sm">
        <div className="flex justify-end p-2 bg-muted/20 border-b">
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleSaveToNotes}
            className="flex items-center gap-1"
            disabled={!chatContent}
          >
            <SaveAll size={16} />
            Save to Notes
          </Button>
        </div>
        
        <div className="flex flex-1 overflow-hidden">
          <div className="w-72 flex-shrink-0 overflow-hidden">
            <DocumentChatSidebar 
              documentList={documents}
              selectedDocumentId={selectedDocumentId}
              onSelect={handleDocumentSelect}
              onNewSession={handleNewSession}
              onDocumentDeleted={handleDocumentDeleted}
              isLoading={isLoadingDocuments}
            />
          </div>
          
          <div className="flex-1 flex overflow-hidden">
            <div className="flex-1 flex-shrink-0 flex flex-col border-r dark:border-border/60 overflow-hidden">
              <DocumentViewer 
                selectedFile={selectedFile} 
                onFileUpload={handleFileUpload} 
                documentUrl={documentUrl || undefined}
              />
            </div>
            
            <div className="w-[450px] flex-shrink-0 overflow-hidden">
              <DocumentChatInterface 
                selectedFile={selectedFile}
                documentUuid={selectedDocumentId || undefined}
                onContentUpdate={handleChatContentUpdate}
              />
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default DocumentChat;
