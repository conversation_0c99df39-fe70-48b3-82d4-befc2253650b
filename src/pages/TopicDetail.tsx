
import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import TopicDetailSidebar from '@/components/TopicDetailSidebar';
import TopicChatInterface from '@/components/TopicChatInterface';
import { useTopicDetail } from '@/hooks/useTopicDetail';
import { TopicDetailData } from '@/types/TopicDetail';
import { useSectors } from '@/hooks/useSectors';
import { fetchMaterialTopicsData } from '@/hooks/useMaterialTopics';
import { fetchClimateRisks, fetchTechnologies } from '@/utils/searchApi';
import { useToast } from '@/hooks/use-toast';

// Import regulatory data
const regulatoryData = [
  {
    id: 1,
    title: "EU Carbon Border Adjustment Mechanism",
    region: "EU",
    status: "Transition Period",
    description: "Import fees on carbon-intensive products, affecting steel, cement, aluminum, fertilizer, and electricity.",
    date: "2023-2026",
  },
  {
    id: 2,
    title: "Corporate Sustainability Reporting Directive",
    region: "EU",
    status: "Enacted",
    description: "Expanded sustainability reporting requirements for large companies, including detailed climate disclosures.",
    date: "2024-2025"
  },
  {
    id: 3,
    title: "SEC Climate Disclosure Rule",
    region: "US",
    status: "Pending",
    description: "Standardized climate risk disclosures for public companies, including emissions data and climate-related risks.",
    date: "2023",
  },
  {
    id: 4,
    title: "National ETS",
    region: "China",
    status: "Active",
    description: "National emissions trading system covering power generation, with expansion to industrial sectors expected.",
    date: "2021-Present"
  },
  {
    id: 5,
    title: "Carbon Tax Implementation",
    region: "India",
    status: "Planning",
    description: "National carbon tax framework being developed to meet NDC targets, focused on high-emission industrial sectors.",
    date: "2023-2024"
  }
];

// Cache for topics data to avoid repeated API calls
const topicsDataCache = new Map<string, { data: TopicDetailData[]; timestamp: number }>();

const TopicDetail: React.FC = () => {
  const { tab, tileId } = useParams<{ tab: string; tileId: string }>();
  const location = useLocation();
  const navigate = useNavigate();
  const { sectors } = useSectors();
  const { toast, dismiss, toasts } = useToast();
  
  const [topics, setTopics] = useState<TopicDetailData[]>([]);
  const [isLoadingTopics, setIsLoadingTopics] = useState(true);
  const [currentTopic, setCurrentTopic] = useState<TopicDetailData | null>(null);
  
  // Get sector name from the first available sector
  const selectedSector = sectors.length > 0 ? sectors[0].name : 'Steel';

  // Memoize current topic to prevent unnecessary re-renders
  const currentTopicMemo = useMemo(() => {
    if (!tileId || topics.length === 0) return null;
    
    const fromState = location.state?.topic;
    if (fromState) return fromState;
    
    const foundTopic = topics.find((t, index) => {
      // Use consistent ID generation: topic.id or (index + 1) to match MaterialTopicsTab
      const topicId = t.id?.toString() || (index + 1).toString();
      return topicId === tileId;
    });
    
    return foundTopic || (topics.length > 0 ? topics[0] : null);
  }, [tileId, topics, location.state?.topic]);

  // Initialize topic detail hook with tab parameter for better caching
  const { chatState, isLoadingChat, sendMessage } = useTopicDetail(
    tileId || '',
    currentTopicMemo?.title || '',
    selectedSector,
    tab // Pass tab for cache key generation
  );

  // Load topics based on the current tab (with caching)
  useEffect(() => {
    const loadTopicsForTab = async () => {
      if (!tab) return;
      
      const cacheKey = `${tab}-${selectedSector}`;
      const cached = topicsDataCache.get(cacheKey);
      
      // Use cache if available and less than 5 minutes old
      if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
        console.log('Using cached topics data for:', tab);
        setTopics(cached.data);
        setIsLoadingTopics(false);
        return;
      }
      
      setIsLoadingTopics(true);
      try {
        let topicsData: any[] = [];
        
        switch (tab) {
          case 'material-topics':
            topicsData = await fetchMaterialTopicsData(selectedSector);
            break;
          case 'climate-risks':
            topicsData = await fetchClimateRisks(selectedSector, { toast, dismiss, toasts }) || [];
            break;
          case 'technologies':
            topicsData = await fetchTechnologies(selectedSector, { toast, dismiss, toasts }) || [];
            break;
          case 'regulatory':
            topicsData = regulatoryData;
            break;
          default:
            console.warn('Unknown tab:', tab);
        }

        // Ensure all topics have IDs
        const formattedTopics = topicsData.map((topic, index) => ({
          ...topic,
          id: topic.id || index + 1
        }));

        setTopics(formattedTopics);
        
        // Cache the result
        topicsDataCache.set(cacheKey, {
          data: formattedTopics,
          timestamp: Date.now()
        });
        
      } catch (error) {
        console.error('Error loading topics:', error);
        toast({
          title: 'Error loading topics',
          description: 'Failed to load topic data. Please try again.',
          variant: 'destructive'
        });
      } finally {
        setIsLoadingTopics(false);
      }
    };

    loadTopicsForTab();
  }, [tab, selectedSector]);

  // Update current topic when topics load or tileId changes
  useEffect(() => {
    setCurrentTopic(currentTopicMemo);
  }, [currentTopicMemo]);

  // Redirect to dashboard if invalid tab
  useEffect(() => {
    if (!tab || !['material-topics', 'climate-risks', 'technologies', 'regulatory'].includes(tab)) {
      navigate('/dashboard');
    }
  }, [tab, navigate]);

  const getTabDisplayName = (tabName: string) => {
    switch (tabName) {
      case 'material-topics':
        return 'Material Topics';
      case 'climate-risks':
        return 'Climate Risks';
      case 'technologies':
        return 'Technologies';
      case 'regulatory':
        return 'Regulatory Landscape';
      default:
        return tabName;
    }
  };

  if (!tab || !tileId) {
    return null;
  }

  return (
    <div className="min-h-screen flex w-full">
      <TopicDetailSidebar
        topics={topics}
        currentTab={tab}
        isLoading={isLoadingTopics}
      />
      
      <div className="flex-1 flex flex-col">
        {isLoadingTopics ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">Loading topics...</p>
            </div>
          </div>
        ) : !currentTopic ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <p className="text-muted-foreground">Topic not found</p>
            </div>
          </div>
        ) : (
          <>
            {/* Header */}
            <div className="border-b p-6">
              <div className="max-w-4xl">
                <h1 className="text-2xl font-bold mb-2">{currentTopic.title}</h1>
                <p className="text-muted-foreground">
                  {getTabDisplayName(tab)} - {selectedSector} Industry
                </p>
              </div>
            </div>

            <div className="flex-1 p-6">
              <div className="max-w-4xl space-y-6">
                {/* Summary Block - Only this section shows loading */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Overview</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {chatState.summary.isLoading ? (
                      <div className="flex items-center space-x-2">
                        <Loader2 className="w-4 h-4 animate-spin" />
                        <span className="text-sm text-muted-foreground">Loading summary...</span>
                      </div>
                    ) : (
                      <p className="text-sm leading-relaxed">
                        {chatState.summary.content || 'Detailed explanation unavailable at the moment.'}
                      </p>
                    )}
                  </CardContent>
                </Card>

                {/* Chat Interface - Now properly isolated per topic */}
                <Card className="flex-1">
                  <CardHeader>
                    <CardTitle className="text-lg">Ask Questions</CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <div className="h-96">
                      <TopicChatInterface
                        messages={chatState.messages}
                        isLoading={isLoadingChat}
                        onSendMessage={sendMessage}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default TopicDetail;
