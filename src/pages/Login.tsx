
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Eye, EyeOff, Check } from 'lucide-react';
import Logo from '@/components/Logo';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import ThemeToggle from '@/components/ThemeToggle';
import { useTheme } from '@/components/ThemeProvider';
import { API_BASE_URL } from '@/utils/endPoints';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { forceRefreshTheme } = useTheme();

  // Ensure login page always starts with light theme
  useEffect(() => {
    // Check if we have a signup theme override and clean it up
    const signupTheme = localStorage.getItem('recrea-signup-theme');
    if (signupTheme) {
      localStorage.removeItem('recrea-signup-theme');
    }

    // Ensure light theme is set
    const currentTheme = localStorage.getItem('recrea-theme');
    if (currentTheme !== 'light') {
      localStorage.setItem('recrea-theme', 'light');
      document.documentElement.classList.remove('dark', 'light');
      document.documentElement.classList.add('light');
      forceRefreshTheme();
    }
  }, [forceRefreshTheme]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      toast({
        title: "All fields are required",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const res = await fetch(API_BASE_URL + '/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          password,
        }),
      });

      if (!res.ok) {
        const errResp = await res.json().catch(() => ({}));
        throw new Error(errResp.message || "Login failed.");
      }

      // Parse the response with user data and token
      const result = await res.json();
      
      // Store access token in localStorage
      localStorage.setItem('accessToken', result.access_token);
      
      // Store user data in localStorage
      localStorage.setItem('userData', JSON.stringify({
        name: result.user.name,
        email: result.user.email,
        phone_number: result.user.phone_number || '',
        company: result.user.company,
        is_active: result.user.is_active,
        uuid: result.user.uuid,
        role: result.user.role || 'User'
      }));

      toast({
        title: "Login Successful!",
        description: "Welcome to Recre8.earth",
      });

      setIsLoading(false);
      navigate('/dashboard'); // Changed from /profile to /dashboard
    } catch (err: any) {
      toast({
        title: "Login failed",
        description: err.message || "An error occurred during login.",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <div className="absolute top-4 right-4 z-10">
        <ThemeToggle />
      </div>
      
      <div className="flex flex-col items-center justify-center flex-1 w-full px-4 py-12">
        <div className="mb-8 animate-fade-in">
          <Logo />
        </div>
        <div className="w-full max-w-md animate-fade-in" style={{ animationDelay: '100ms' }}>
          <div className="text-center mb-6">
            <p className="text-muted-foreground">Recreating a better tomorrow</p>
          </div>
          <div className="glass-card p-8">
            {showConfirmation ? (
              <div className="text-center space-y-4">
                <div className="flex justify-center mb-4">
                  <div className="rounded-full bg-recrea-turquoise/20 p-3">
                    <Check className="h-8 w-8 text-recrea-turquoise" />
                  </div>
                </div>
                <h2 className="text-xl font-semibold">Thank you!</h2>
                <p className="text-muted-foreground">
                  Your user ID and default password have been sent to your registered email. 
                  Please log in and update your password.
                </p>
                <Button 
                  className="primary-button mt-4 w-full"
                  onClick={() => setShowConfirmation(false)}
                >
                  Return to Login
                </Button>
              </div>
            ) : (
              <>
                <h2 className="text-2xl font-semibold text-center mb-6">Welcome</h2>
                
                <div className="flex rounded-lg overflow-hidden mb-6 border border-input">
                  <button 
                    className="flex-1 py-3 text-center font-medium bg-primary text-primary-foreground"
                  >
                    Login
                  </button>
                  <button 
                    className="flex-1 py-3 text-center font-medium bg-transparent text-muted-foreground hover:text-recrea-turquoise transition-colors"
                    onClick={() => navigate('/signup')}
                  >
                    Sign Up
                  </button>
                </div>
                
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <Input
                      type="email"
                      placeholder="Email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="glass-input w-full"
                    />
                  </div>
                  
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      placeholder="Password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="glass-input w-full"
                    />
                    <button 
                      type="button"
                      className="absolute right-3 top-3 text-muted-foreground hover:text-foreground"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                  
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="primary-button w-full"
                  >
                    {isLoading ? "Logging in..." : "Login"}
                  </Button>
                </form>
              </>
            )}
          </div>
        </div>
      </div>
      
      <div className="absolute top-0 left-0 w-full h-full -z-10 pointer-events-none overflow-hidden">
        <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-recrea-turquoise/5 rounded-full blur-3xl transform translate-x-1/3 -translate-y-1/3"></div>
        <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-recrea-mint/5 rounded-full blur-3xl transform -translate-x-1/3 translate-y-1/3"></div>
      </div>
    </div>
  );
};

export default Login;
