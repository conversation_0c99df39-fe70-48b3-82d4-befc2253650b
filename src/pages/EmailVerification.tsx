import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Check, X, Loader2, Mail } from 'lucide-react';
import Logo from '@/components/Logo';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import ThemeToggle from '@/components/ThemeToggle';
import { verifyEmail } from '@/api/commonApi';

const EmailVerification = () => {
  const [verificationStatus, setVerificationStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState('');
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const navigate = useNavigate();

  const token = searchParams.get('token');

  useEffect(() => {
    const handleVerifyEmail = async () => {
      if (!token) {
        setVerificationStatus('error');
        setErrorMessage('Invalid verification link. No token provided.');
        return;
      }

      try {
        console.log('Attempting to verify email with token:', token);
        const data = await verifyEmail(token);
        console.log('Verification response:', data);

        // Check the status from the JSON response
        if (data.status === 'success') {
          setVerificationStatus('success');

          toast({
            title: "Email verified successfully!",
            description: data.message || "Your account has been verified. You can now log in.",
          });

          // Redirect to login after 3 seconds
          setTimeout(() => {
            navigate('/login');
          }, 3000);
        } else {
          // Handle unexpected status
          setVerificationStatus('error');
          setErrorMessage(data.message || "Unexpected response from server.");

          toast({
            title: "Verification failed",
            description: data.message || "Unexpected response from server.",
            variant: "destructive",
          });
        }

      } catch (err: any) {
        console.error("Verification error:", err);
        setVerificationStatus('error');
        setErrorMessage(err.message || "An error occurred during verification.");

        toast({
          title: "Verification failed",
          description: err.message || "An error occurred during verification.",
          variant: "destructive",
        });
      }
    };

    handleVerifyEmail();
  }, [token, toast, navigate]);

  const handleRetryVerification = () => {
    setVerificationStatus('loading');
    setErrorMessage('');
    // Re-trigger verification
    window.location.reload();
  };

  const handleGoToLogin = () => {
    navigate('/login');
  };

  const handleGoToSignup = () => {
    navigate('/signup');
  };

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <div className="absolute top-4 right-4 z-10">
        <ThemeToggle />
      </div>
      
      <div className="flex flex-col items-center justify-center flex-1 w-full px-4 py-12">
        <div className="mb-8 animate-fade-in">
          <Logo />
        </div>
        <div className="w-full max-w-md animate-fade-in" style={{ animationDelay: '100ms' }}>
          <div className="text-center mb-6">
            <p className="text-muted-foreground">Email Verification</p>
          </div>
          
          <div className="glass-card p-8">
            <div className="text-center space-y-4">
              {verificationStatus === 'loading' && (
                <>
                  <div className="flex justify-center mb-4">
                    <div className="rounded-full bg-blue-500/20 p-3">
                      <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
                    </div>
                  </div>
                  <h2 className="text-xl font-semibold">Verifying your email...</h2>
                  <p className="text-muted-foreground">
                    Please wait while we verify your email address.
                  </p>
                </>
              )}

              {verificationStatus === 'success' && (
                <>
                  <div className="flex justify-center mb-4">
                    <div className="rounded-full bg-recrea-turquoise/20 p-3">
                      <Check className="h-8 w-8 text-recrea-turquoise" />
                    </div>
                  </div>
                  <h2 className="text-xl font-semibold">Email verified successfully!</h2>
                  <p className="text-muted-foreground">
                    Your account has been verified. You will be redirected to the login page in a few seconds.
                  </p>
                  <Button 
                    className="primary-button mt-4 w-full"
                    onClick={handleGoToLogin}
                  >
                    Go to Login Now
                  </Button>
                </>
              )}

              {verificationStatus === 'error' && (
                <>
                  <div className="flex justify-center mb-4">
                    <div className="rounded-full bg-red-500/20 p-3">
                      <X className="h-8 w-8 text-red-500" />
                    </div>
                  </div>
                  <h2 className="text-xl font-semibold">Verification failed</h2>
                  <p className="text-muted-foreground">
                    {errorMessage || "We couldn't verify your email address. This could be due to an expired or invalid verification link."}
                  </p>
                  
                  <div className="space-y-2 mt-6">
                    <Button 
                      className="primary-button w-full"
                      onClick={handleRetryVerification}
                    >
                      Try Again
                    </Button>
                    <Button 
                      variant="outline"
                      className="w-full"
                      onClick={handleGoToSignup}
                    >
                      Request New Verification Email
                    </Button>
                    <Button 
                      variant="ghost"
                      className="w-full"
                      onClick={handleGoToLogin}
                    >
                      Go to Login
                    </Button>
                  </div>
                </>
              )}
            </div>
          </div>
          
          <div className="text-center mt-6">
            <p className="text-sm text-muted-foreground">
              Need help? Contact our support team.
            </p>
          </div>
        </div>
      </div>
      
      <div className="absolute top-0 left-0 w-full h-full -z-10 pointer-events-none overflow-hidden">
        <div className="absolute w-full h-40 bg-gradient-to-t from-background/50 to-transparent bottom-0"></div>
      </div>
    </div>
  );
};

export default EmailVerification;
