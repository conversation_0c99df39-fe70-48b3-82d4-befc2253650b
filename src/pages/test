{"user_uuid": "01981831-e595-7c02-ae88-99f2e7790824", "sector_uuid": "019657e2-9a0e-7781-8647-0938391ba11b", "name": "scenario 24", "flow_type": "SCENARIO_MAIN", "flow_diagram": {"nodes": {"1": {"activity": "Gas Oil Separation", "position": {"x": 150, "y": 120}, "technologies": [{"name": "Boiler", "inputs": {"energies": [], "emissions": [], "materials": [{"id": "material-input-0", "cost": 1, "unit": "<PERSON><PERSON>", "material": "Crude oil", "quantity": 1, "specificMaterialCost": 1}]}, "endYear": 2035, "outputs": {"energies": [], "materials": [{"id": "material-output-0", "unit": "<PERSON><PERSON>", "material": "Raw gas", "quantity": 1, "targetNodeId": "2", "isFinalOutput": false, "targetTechnology": "Boiler", "specificMaterialCost": 3}]}, "isCustom": false, "financial": {"capacity": 1, "capitalCost": 0, "capacityUnit": "Units/day", "capitalCostUnit": "1", "operatingMaintenanceCost": 1, "operatingMaintenanceCostUnit": "USD/year"}, "startYear": 2025, "byproducts": {"energies": [], "materials": []}}]}, "2": {"activity": "Condensation", "position": {"x": 500, "y": 120}, "technologies": [{"name": "Boiler", "inputs": {"energies": [], "emissions": [], "materials": [{"id": "material-input-0", "cost": 1, "unit": "<PERSON><PERSON>", "material": "Raw gas", "quantity": 1, "sourceNodeId": "1", "sourceTechnology": "Boiler", "specificMaterialCost": 1}]}, "endYear": 2035, "outputs": {"energies": [], "materials": [{"id": "material-output-0", "unit": "<PERSON><PERSON>", "material": "Sour gas", "quantity": 1, "targetNodeId": "3", "isFinalOutput": false, "targetTechnology": "Boiler", "specificMaterialCost": 3}]}, "isCustom": false, "financial": {"capacity": 1, "capitalCost": 0, "capacityUnit": "Units/day", "capitalCostUnit": "1", "operatingMaintenanceCost": 1, "operatingMaintenanceCostUnit": "USD/year"}, "startYear": 2025, "byproducts": {"energies": [], "materials": []}}]}, "3": {"activity": "Amine Absorbing", "position": {"x": 850, "y": 120}, "technologies": [{"name": "Boiler", "inputs": {"energies": [], "emissions": [], "materials": [{"id": "material-input-0", "cost": 1, "unit": "<PERSON><PERSON>", "material": "Sour gas", "quantity": 1, "sourceNodeId": "2", "sourceTechnology": "Boiler", "specificMaterialCost": 1}]}, "endYear": 2035, "outputs": {"energies": [], "materials": [{"id": "material-output-0", "unit": "<PERSON><PERSON>", "material": "Sweet gas", "quantity": 1, "targetNodeId": "4", "isFinalOutput": false, "targetTechnology": "Boiler", "specificMaterialCost": 3}]}, "isCustom": false, "financial": {"capacity": 1, "capitalCost": 0, "capacityUnit": "Units/day", "capitalCostUnit": "1", "operatingMaintenanceCost": 1, "operatingMaintenanceCostUnit": "USD/year"}, "startYear": 2025, "byproducts": {"energies": [], "materials": []}}]}, "4": {"activity": "Water Saturation", "position": {"x": 1200, "y": 120}, "technologies": [{"name": "Boiler", "inputs": {"energies": [], "emissions": [], "materials": [{"id": "material-input-0", "cost": 1, "unit": "<PERSON><PERSON>", "material": "Sweet gas", "quantity": 1, "sourceNodeId": "3", "sourceTechnology": "Boiler", "specificMaterialCost": 1}]}, "endYear": 2035, "outputs": {"energies": [], "materials": [{"id": "material-output-0", "unit": "<PERSON><PERSON>", "material": "Saturated gas", "quantity": 1, "targetNodeId": "5", "isFinalOutput": false, "targetTechnology": "Boiler", "specificMaterialCost": 3}]}, "isCustom": false, "financial": {"capacity": 1, "capitalCost": 0, "capacityUnit": "Units/day", "capitalCostUnit": "1", "operatingMaintenanceCost": 1, "operatingMaintenanceCostUnit": "USD/year"}, "startYear": 2025, "byproducts": {"energies": [], "materials": []}}]}, "5": {"activity": "Glycol Contacting", "position": {"x": 1200, "y": 370}, "technologies": [{"name": "Boiler", "inputs": {"energies": [], "emissions": [], "materials": [{"id": "material-input-0", "cost": 1, "unit": "<PERSON><PERSON>", "material": "Saturated gas", "quantity": 1, "sourceNodeId": "4", "sourceTechnology": "Boiler", "specificMaterialCost": 1}]}, "endYear": 2035, "outputs": {"energies": [], "materials": [{"id": "material-output-0", "unit": "<PERSON><PERSON>", "material": "Dry gas", "quantity": 1, "targetNodeId": "6", "isFinalOutput": false, "targetTechnology": "Boiler", "specificMaterialCost": 3}]}, "isCustom": false, "financial": {"capacity": 1, "capitalCost": 0, "capacityUnit": "Units/day", "capitalCostUnit": "1", "operatingMaintenanceCost": 1, "operatingMaintenanceCostUnit": "USD/year"}, "startYear": 2025, "byproducts": {"energies": [], "materials": []}}]}, "6": {"activity": "Mercury Removal", "position": {"x": 850, "y": 370}, "technologies": [{"name": "Boiler", "inputs": {"energies": [{"id": "energy-1750083848937", "cost": 0, "unit": "GJ", "energy": "", "quantity": 1, "specificEnergyCost": 0}], "emissions": [{"id": "emission-1750083848937", "cost": 0, "unit": "kg", "emission": "", "emissionFactor": 0}], "materials": [{"id": "material-input-0", "cost": 1, "unit": "<PERSON><PERSON>", "material": "Dry gas", "quantity": 1, "sourceNodeId": "5", "sourceTechnology": "Boiler", "specificMaterialCost": 1}]}, "endYear": 2035, "outputs": {"energies": [{"id": "energy-1750083852004-0", "unit": "GJ", "energy": "Energy 2", "quantity": 1, "targetNodeId": null, "isFinalOutput": true, "targetTechnology": null, "specificEnergyCost": 0.8}], "materials": [{"id": "material-output-0", "unit": "<PERSON><PERSON>", "material": "Purified gas", "quantity": 1, "targetNodeId": "7", "isFinalOutput": false, "targetTechnology": "Boiler", "specificMaterialCost": 3}]}, "isCustom": false, "financial": {"capacity": 1, "capitalCost": 0, "capacityUnit": "Units/day", "capitalCostUnit": "1", "operatingMaintenanceCost": 1, "operatingMaintenanceCostUnit": "USD/year"}, "startYear": 2025, "byproducts": {"energies": [], "materials": []}}]}, "7": {"activity": "Mercury Gas Condensation", "position": {"x": 500, "y": 370}, "technologies": [{"name": "Boiler", "inputs": {"energies": [], "emissions": [], "materials": [{"id": "material-input-0", "cost": 1, "unit": "<PERSON><PERSON>", "material": "Purified gas", "quantity": 1, "sourceNodeId": "6", "sourceTechnology": "Boiler", "specificMaterialCost": 1}]}, "endYear": 2035, "outputs": {"energies": [], "materials": [{"id": "material-output-0", "unit": "<PERSON><PERSON>", "material": "Processed gas", "quantity": 1, "targetNodeId": "8", "isFinalOutput": false, "targetTechnology": "Boiler", "specificMaterialCost": 3}]}, "isCustom": false, "financial": {"capacity": 1, "capitalCost": 0, "capacityUnit": "Units/day", "capitalCostUnit": "1", "operatingMaintenanceCost": 1, "operatingMaintenanceCostUnit": "USD/year"}, "startYear": 2025, "byproducts": {"energies": [], "materials": []}}]}, "8": {"activity": "Cryogenic Seperation", "position": {"x": 150, "y": 370}, "technologies": [{"name": "Boiler", "inputs": {"energies": [], "emissions": [], "materials": [{"id": "material-input-0", "cost": 1, "unit": "<PERSON><PERSON>", "material": "Processed gas", "quantity": 1, "sourceNodeId": "7", "sourceTechnology": "Boiler", "specificMaterialCost": 1}]}, "endYear": 2035, "outputs": {"energies": [], "materials": [{"id": "material-output-0", "unit": "<PERSON><PERSON>", "material": "Natural gas", "quantity": 1, "targetNodeId": "natural-gas", "isFinalOutput": true, "targetTechnology": "Boiler", "specificMaterialCost": 3}]}, "isCustom": false, "financial": {"capacity": 1, "capitalCost": 0, "capacityUnit": "Units/day", "capitalCostUnit": "1", "operatingMaintenanceCost": 1, "operatingMaintenanceCostUnit": "USD/year"}, "startYear": 2025, "byproducts": {"energies": [], "materials": []}}]}}, "metadata": {"createdAt": "2025-07-22T04:17:52.188Z", "updatedAt": "2025-07-22T04:17:52.188Z"}}, "period_type": "single", "period_length": 3, "number_of_periods": 1, "start_date": "2025-07-21", "base_scenario_id": null, "created_at": "2025-07-22T04:17:53.012732"}