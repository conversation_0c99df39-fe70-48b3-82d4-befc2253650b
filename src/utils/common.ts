// Common utility functions for the app
import { jwtDecode } from "jwt-decode";

// Converts a string to a URL-friendly slug for activities
export function getActivitySlug(title: string): string {
  return title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
}

// Capitalizes the first letter of a string
export function capitalizeFirstLetter(str: string) {
  return str ? str.charAt(0).toUpperCase() + str.slice(1) : '';
} 

export function isTokenExpired(token: string): boolean {
  try {
    const decoded: { exp: number } = jwtDecode(token);
    if (!decoded.exp) return true;
    // exp is in seconds, Date.now() in ms
    return decoded.exp * 1000 < Date.now();
  } catch {
    return true; // If decode fails, treat as expired
  }
} 

export function logout () {
  localStorage.clear();
  sessionStorage.clear();
  window.location.href = "/login";
}

export function scheduleAutoLogout(token: string) {
  const decoded: { exp: number } = jwtDecode(token);
  if (decoded && decoded.exp) {
    const msUntilExpiry = decoded.exp * 1000 - Date.now();
      setTimeout(() => {
        logout();
      }, msUntilExpiry);
  }
}