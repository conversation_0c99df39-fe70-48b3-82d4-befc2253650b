// Common API utilities for the app

export const API_BASE_URL = 'https://api.recre8.earth';

export function getAuthHeaders() {
  const accessToken = localStorage.getItem('accessToken');
  if (!accessToken) {
    throw new Error('No access token found. Please log in again.');
  }
  return {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json',
  };
}

export async function fetchRegions() {
  const res = await fetch(`${API_BASE_URL}/regions`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });
  if (!res.ok) throw new Error('Failed to fetch regions');
  return await res.json();
}

export async function fetchExperts() {
  const res = await fetch(`${API_BASE_URL}/experts`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });
  if (!res.ok) throw new Error('Failed to fetch experts');
  return await res.json();
}

export async function fetchExpertsByActivity(activityName: string) {
  const res = await fetch(`${API_BASE_URL}/experts/by-activity/${activityName}`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });
  if (!res.ok) throw new Error('Failed to fetch experts for this activity');
  return await res.json();
}

export async function verifyEmail(token: string) {
  const res = await fetch(`${API_BASE_URL}/verify-email?token=${token}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    mode: 'cors'
  });

  if (!res.ok) {
    const errResp = await res.json().catch(() => ({}));
    throw new Error(errResp.message || `Verification failed with status: ${res.status}`);
  }

  return await res.json();
}