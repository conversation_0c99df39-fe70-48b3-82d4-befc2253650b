import { API_BASE_URL } from "@/utils/endPoints";

export async function fetchTechnologyByName(techName: string) {
  const token = localStorage.getItem('accessToken');
  const res = await fetch(`${API_BASE_URL}/resources/technologies/name/${techName}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
  if (!res.ok) throw new Error('Failed to fetch technology details');
  return await res.json();
}

export async function fetchSuppliersByTechnology(techName: string) {
  const token = localStorage.getItem('accessToken');
  const res = await fetch(`${API_BASE_URL}/suppliers/by-technology/${techName}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
  if (!res.ok) throw new Error('Failed to fetch suppliers');
  return await res.json();
} 