import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useSectors } from '@/hooks/useSectors';
import { API_BASE_URL } from '@/utils/endPoints';

interface Sector {
  uuid: string;
  name: string;
  default_flow: string | null;
}

interface Activity {
  uuid: string;
  name: string;
  icon: string | null;
  subtext: string | null;
}

interface SectorActivitiesContextType {
  sectors: Sector[];
  setSectors: (sectors: Sector[]) => void;
  loadingSectors: boolean;
  selectedSector: string;
  setSelectedSector: (slug: string) => void;
  activities: Activity[];
  loadingActivities: boolean;
  activitiesError: string | null;
}

export const SectorActivitiesContext = createContext<SectorActivitiesContextType>({} as SectorActivitiesContextType);

export const useSectorActivities = () => useContext(SectorActivitiesContext);

export const SectorActivitiesProvider = ({ children }: { children: ReactNode }) => {
  
  const { sectors: fetchedSectors, isLoading: loadingSectors } = useSectors(true);
  const [sectors, setSectors] = useState<Sector[]>([]);
  const [selectedSector, setSelectedSector] = useState<string>('');
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loadingActivities, setLoadingActivities] = useState(false);
  const [activitiesError, setActivitiesError] = useState<string | null>(null);

  // Update sectors from fetchedSectors if local sectors is empty
  useEffect(() => {
    if (fetchedSectors.length > 0 && sectors.length === 0) {
      setSectors(fetchedSectors);
    }
  }, [fetchedSectors, sectors.length]);

  // Set default sector when sectors load
  useEffect(() => {
    if (sectors.length > 0 && !selectedSector) {
      setSelectedSector(sectors[0].name.toLowerCase().replace(/\s+/g, '-'));
    }
  }, [sectors, selectedSector]);

  // Find sector UUID from selected sector slug
  const getSectorUuid = (sectorSlug: string) => {
    const sectorObj = sectors.find(s => s.name.toLowerCase().replace(/\s+/g, '-') === sectorSlug);
    return sectorObj ? sectorObj.uuid : null;
  };

  // Fetch activities when selectedSector or sectors change
  useEffect(() => {
    if (sectors.length === 0 || !selectedSector) return;
    const uuid = getSectorUuid(selectedSector);
    if (!uuid) {
      setActivities([]);
      return;
    }
    setLoadingActivities(true);
    setActivitiesError(null);
    const token = localStorage.getItem('accessToken');
    if (!token) {
      setActivitiesError('No access token found. Please log in again.');
      setActivities([]);
      setLoadingActivities(false);
      return;
    }
    fetch(`${API_BASE_URL}/resources/sectors/${uuid}/activities`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    })
      .then(res => {
        if (!res.ok) throw new Error('Failed to fetch activities');
        return res.json();
      })
      .then(data => setActivities(data))
      .catch(err => {
        setActivitiesError(err.message || 'Failed to fetch activities');
        setActivities([]);
      })
      .finally(() => setLoadingActivities(false));
  }, [selectedSector, sectors]);

  return (
    <SectorActivitiesContext.Provider
      value={{
        sectors,
        setSectors,
        loadingSectors,
        selectedSector,
        setSelectedSector,
        activities,
        loadingActivities,
        activitiesError,
      }}
    >
      {children}
    </SectorActivitiesContext.Provider>
  );
}; 