
import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'dark' | 'light';

type ThemeContextType = {
  theme: Theme;
  toggleTheme: () => void;
  forceRefreshTheme: () => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Initialize state with a default value instead of trying to access localStorage synchronously
  const [theme, setTheme] = useState<Theme>('light');

  // Use useEffect to handle localStorage operations after component mounts
  useEffect(() => {
    const storedTheme = localStorage.getItem('recrea-theme') as Theme | null;
    if (storedTheme) {
      setTheme(storedTheme);
    }
    // No else clause needed as we already set the default to 'light'
  }, []);

  useEffect(() => {
    document.documentElement.classList.remove('dark', 'light');
    document.documentElement.classList.add(theme);
    // Also update localStorage when theme changes
    localStorage.setItem('recrea-theme', theme);
  }, [theme]);

  const toggleTheme = () => {
    setTheme(prevTheme => prevTheme === 'dark' ? 'light' : 'dark');
  };

  const forceRefreshTheme = () => {
    const storedTheme = localStorage.getItem('recrea-theme') as Theme | null;
    if (storedTheme) {
      setTheme(storedTheme);
    } else {
      setTheme('light');
    }
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, forceRefreshTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
