
import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useTheme } from '@/components/ThemeProvider';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import AppSidebar from "@/components/AppSidebar";
// import OptimizerSidebar from "@/components/OptimizerSidebar";
import { logout, scheduleAutoLogout } from '@/utils/common';
// import { jwtDecode } from "jwt-decode";

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { theme } = useTheme();
  const location = useLocation();

  useEffect(() => {
    const token = localStorage.getItem('accessToken');
    if(token){
      scheduleAutoLogout(token);
    }else{
      logout();
    }
  }, [location])

  // For optimizer context, let the page handle its own sidebar
  const isOptimizerContext = location.pathname === '/optimizer' || location.pathname.startsWith('/industry-flow');
  // For industry-flow pages, let them handle their own header completely
  const isIndustryFlowContext = location.pathname.startsWith('/industry-flow');

  return (
    <div className={`min-h-screen flex w-full ${theme === 'dark' ? 'bg-recrea-dark text-white' : 'bg-white text-recrea-dark'}`}>
      <SidebarProvider>
        {!isOptimizerContext && <AppSidebar />}
        <div className="flex flex-col w-full">
          {!isIndustryFlowContext && <Header />}
          <div className="flex-1">
            {children}
          </div>
          <Footer />
        </div>
      </SidebarProvider>
    </div>
  );
};

export default Layout;
