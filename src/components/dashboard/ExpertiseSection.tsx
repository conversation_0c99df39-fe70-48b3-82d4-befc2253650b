import React from 'react';
import { User, <PERSON>ed<PERSON>, Star } from 'lucide-react';

interface Region {
  code: string;
  name: string;
}

interface Expert {
  name: string;
  about: string;
  highestEducation: string;
  linkedin?: string;
  experience: number;
  region: Region;
  languages: string[];
  expertise: string[];
  charge: number;
  rating: number;
  projects: number;
}

interface ExpertiseSectionProps {
  experts: Expert[];
  loading?: boolean;
  error?: string;
}

// Skeleton card for loading state
const SkeletonExpertCard = () => (
  <div className="rounded-xl border p-6 flex flex-col gap-3 shadow-sm animate-pulse bg-white">
    <div className="flex items-center gap-4 mb-2">
      <div className="w-14 h-14 rounded-full bg-gray-200 flex items-center justify-center" />
      <div className="flex-1">
        <div className="h-4 bg-gray-200 rounded w-24 mb-2" />
        <div className="h-3 bg-gray-100 rounded w-20" />
      </div>
      <div className="h-5 w-5 bg-gray-200 rounded-full ml-auto" />
    </div>
    <div className="h-3 bg-gray-100 rounded w-full mb-1" />
    <div className="h-3 bg-gray-100 rounded w-2/3 mb-1" />
    <div className="flex items-center gap-2 mb-1">
      <div className="h-4 w-4 bg-gray-200 rounded-full" />
      <div className="h-3 bg-gray-100 rounded w-10" />
    </div>
    <div className="h-3 bg-gray-100 rounded w-1/2 mb-1" />
    <div className="h-3 bg-gray-100 rounded w-1/3 mb-1" />
    <div className="h-3 bg-gray-100 rounded w-2/5 mb-1" />
    <div className="flex flex-wrap gap-2 mb-2">
      <div className="h-5 w-16 bg-gray-100 rounded-full" />
      <div className="h-5 w-12 bg-gray-100 rounded-full" />
    </div>
    <div className="h-4 bg-gray-200 rounded w-28 mt-auto" />
  </div>
);

const ExpertiseSection: React.FC<ExpertiseSectionProps> = ({ experts, loading, error }) => {
  return (
    <section className="rounded-xl">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {loading ? (
          // Show 6 skeleton cards for loading state
          Array.from({ length: 6 }).map((_, idx) => <SkeletonExpertCard key={idx} />)
        ) : error ? (
          <div className="py-12 text-center text-red-500 col-span-full">{error}</div>
        ) : 
        experts && experts.length === 0 ? (
          <div className="py-12 text-center text-muted-foreground col-span-full">No experts found</div>
        ) : (
          experts?.map((expert, idx) => (
            <div key={idx} className="rounded-xl border p-6 flex flex-col gap-3 shadow-sm">
              <div className="flex items-center gap-4 mb-2">
                <div className="w-14 h-14 rounded-full bg-gray-100 flex items-center justify-center text-gray-400 text-2xl font-bold">
                  <User className="h-8 w-8 text-gray-400" />
                </div>
                <div>
                  <div className="font-semibold text-lg">{expert.name}</div>
                  <div className="text-muted-foreground text-sm">{expert.highestEducation}</div>
                </div>
                {expert.linkedin && (
                  <a href={expert.linkedin} target="_blank" rel="noopener noreferrer" className="ml-auto" style={{ color: '#0A66C2' }}>
                    <Linkedin className="h-5 w-5" />
                  </a>
                )}
              </div>
              <div className="text-muted-foreground text-sm mb-1 line-clamp-2" style={{display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical', overflow: 'hidden'}}>
                {expert.about}
              </div>
              <div className="flex items-center gap-2 text-green-700 font-semibold text-base mb-1">
                <Star className="h-4 w-4 fill-green-600 text-green-600" />
                {expert.rating} <span className="text-muted-foreground font-normal text-sm">({expert.projects} projects)</span>
              </div>
              <div className="text-sm mb-1"><span className="font-semibold">Experience:</span> {expert.experience} years</div>
              <div className="text-sm mb-1"><span className="font-semibold">Region:</span> {expert.region?.name}</div>
              <div className="text-sm mb-1"><span className="font-semibold">Languages:</span> {expert.languages.join(', ')}</div>
              {expert?.expertise?.length > 0 ? <div className="text-sm mb-2"><span className="font-semibold">Expertise:</span> {expert.expertise?.map((ex, i) => (
                <span key={i} className="inline-block bg-recrea-light-mint text-green-700 rounded-full px-3 py-0.5 text-xs font-medium mr-2 mb-1 border border-green-200">{ex}</span>
              ))}</div>
              : null }
              <div className="text-sm font-semibold text-green-700 mt-auto">Consultation: ${expert.charge}/hour</div>
            </div>
          ))
        )}
      </div>
    </section>
  );
};

export default ExpertiseSection;
