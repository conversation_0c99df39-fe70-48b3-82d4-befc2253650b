import React, { useState, useEffect } from 'react';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Link, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { API_BASE_URL } from '@/utils/endPoints';

interface PeerData {
  name: string;
  emissionsReduction: number;
  energyEfficiency: number;
  renewableEnergy: number;
  carbonIntensity: number;
}

interface PerformanceData {
  emissionsReduction: number;
  energyEfficiency: number;
  renewableEnergy: number;
  carbonIntensity: number;
}

interface SectorUpdate {
  title: string;
  url: string;
  snippet: string;
  update_type: string;
  update_date: string;
  category: string;
}

interface SectorUpdatesResponse {
  sector: string;
  updates: SectorUpdate[];
}

type UpdateCategory = 'Policy' | 'Technology & Innovation' | 'Climate Risks';

interface PerformanceTabProps {
  peerPerformanceData: PeerData[];
  performanceData: PerformanceData;
  selectedSector?: string;
  sectors?: Array<{ name: string; uuid: string }>;
}

// Cache for sector updates to avoid refetching during the session
const updateCache: Record<string, SectorUpdate[]> = {};

// Add a helper function to normalize category names
const normalizeCategory = (category: string): UpdateCategory => {
  const lowerCategory = category.toLowerCase();
  
  if (lowerCategory.includes('policy')) {
    return 'Policy';
  } else if (lowerCategory.includes('tech') || lowerCategory.includes('innov')) {
    return 'Technology & Innovation';
  } else if (lowerCategory.includes('climate') || lowerCategory.includes('risk')) {
    return 'Climate Risks';
  }
  
  // Default to Policy if unknown
  return 'Policy';
};

const PerformanceTab: React.FC<PerformanceTabProps> = ({
  peerPerformanceData,
  performanceData,
  selectedSector,
  sectors
}) => {
  const [sectorUpdates, setSectorUpdates] = useState<SectorUpdate[]>([]);
  const [isLoadingUpdates, setIsLoadingUpdates] = useState<boolean>(false);
  const [updateError, setUpdateError] = useState<string | null>(null);
  const [activeUpdateCategory, setActiveUpdateCategory] = useState<UpdateCategory>('Policy');
  const { toast } = useToast();

  useEffect(() => {
    const fetchSectorUpdates = async () => {
      if (!selectedSector || !sectors || sectors.length === 0) return;

      // Find the sector name from the selected sector value
      const sectorObj = sectors.find(s => s.name.toLowerCase().replace(/\s+/g, '-') === selectedSector);
      if (!sectorObj) return;

      const sectorName = sectorObj.name;
      
      // Check if we have cached data for this sector
      if (updateCache[sectorName]) {
        console.log('Using cached sector updates for:', sectorName);
        setSectorUpdates(updateCache[sectorName]);
        return;
      }
      
      setIsLoadingUpdates(true);
      setUpdateError(null);

      try {
        const accessToken = localStorage.getItem('accessToken');
        if (!accessToken) {
          toast({
            title: "Authentication Required",
            description: "Please log in to view sector updates",
            variant: "destructive",
          });
          return;
        }

        const response = await fetch(`${API_BASE_URL}/sector-updates/${encodeURIComponent(sectorName)}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch sector updates: ${response.statusText}`);
        }

        const data: SectorUpdatesResponse = await response.json();
        
        // Log the full API response structure for debugging
        console.log('API Response Structure:', JSON.stringify(data, null, 2));
        
        // Check if updates exist and have the expected structure
        if (data.updates && Array.isArray(data.updates)) {
          // Log individual categories for debugging
          const categories = data.updates.map(update => update.category || 'undefined');
          const uniqueCategories = [...new Set(categories)];
          console.log('Categories in response:', uniqueCategories);
          
          // Cache the fetched updates
          updateCache[sectorName] = data.updates;
        } else {
          console.warn('Unexpected API response structure:', data);
        }
        
        setSectorUpdates(data.updates || []);
        console.log('Fetched sector updates:', data.updates);
      } catch (error) {
        console.error('Error fetching sector updates:', error);
        setUpdateError('Failed to load sector updates. Please try again later.');
        toast({
          title: "Error",
          description: "Failed to fetch sector updates.",
          variant: "destructive",
        });
      } finally {
        setIsLoadingUpdates(false);
      }
    };

    fetchSectorUpdates();
  }, [selectedSector, sectors, toast]);

  const formatDate = (dateString: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };

  // Filter updates by category - normalize categories to handle potential case differences
  const getUpdatesByCategory = (categoryName: string) => {
    return sectorUpdates.filter(update => {
      // If category is missing, try to infer from update_type or assign to default
      const updateCategory = update.category 
        ? normalizeCategory(update.category)
        : update.update_type 
          ? normalizeCategory(update.update_type)
          : 'Policy';
          
      return updateCategory === categoryName;
    });
  };

  // Get updates for each category
  const policyUpdates = getUpdatesByCategory('Policy');
  const techInnovationUpdates = getUpdatesByCategory('Technology & Innovation');
  const climateRiskUpdates = getUpdatesByCategory('Climate Risks');
  
  // Debug the update categorization
  useEffect(() => {
    if (sectorUpdates.length > 0) {
      console.log('Total updates:', sectorUpdates.length);
      console.log('Policy updates:', policyUpdates.length);
      console.log('Tech & Innovation updates:', techInnovationUpdates.length);
      console.log('Climate Risk updates:', climateRiskUpdates.length);
      console.log('Categories present:', [...new Set(sectorUpdates.map(u => u.category))]);
    }
  }, [sectorUpdates, policyUpdates, techInnovationUpdates, climateRiskUpdates]);

  return (
    <div className="glass-card p-4 md:p-8">
      <h2 className="text-xl md:text-2xl font-semibold mb-6 break-words">Peer Performance Comparison</h2>
      <p className="text-muted-foreground mb-6">
        Industry-wide sustainability performance indicators and peer comparison.
      </p>
      
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Company</TableHead>
              <TableHead>Emissions Reduction</TableHead>
              <TableHead>Energy Efficiency</TableHead>
              <TableHead>Renewable Energy</TableHead>
              <TableHead>Carbon Intensity</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {peerPerformanceData.map((peer, index) => (
              <TableRow key={index}>
                <TableCell>{peer.name}</TableCell>
                <TableCell>{peer.emissionsReduction}%</TableCell>
                <TableCell>{peer.energyEfficiency}%</TableCell>
                <TableCell>{peer.renewableEnergy}%</TableCell>
                <TableCell>{peer.carbonIntensity}%</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      
      {/* Sector Updates section with tabs */}
      <h2 className="text-xl md:text-2xl font-semibold mt-10 mb-6 break-words">Sector Updates</h2>
      <p className="text-muted-foreground mb-6">
        Latest news, policy changes, and innovations in your industry sector.
      </p>
      
      {isLoadingUpdates ? (
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">Loading sector updates...</span>
        </div>
      ) : updateError ? (
        <div className="p-6 text-center">
          <p className="text-destructive">{updateError}</p>
        </div>
      ) : sectorUpdates.length === 0 ? (
        <div className="p-6 text-center">
          <p className="text-muted-foreground">No sector updates available at this time.</p>
        </div>
      ) : (
        <Tabs defaultValue="Policy" className="w-full">
          <TabsList className="mb-8 grid w-full grid-cols-3">
            <TabsTrigger 
              value="Policy"
              className={`data-[state=active]:bg-recrea-turquoise data-[state=active]:text-white`}
            >
              Policy Updates
            </TabsTrigger>
            <TabsTrigger 
              value="Technology & Innovation"
              className={`data-[state=active]:bg-recrea-teal data-[state=active]:text-white`}
            >
              Technology & Innovation
            </TabsTrigger>
            <TabsTrigger 
              value="Climate Risks"
              className={`data-[state=active]:bg-recrea-green data-[state=active]:text-white`}
            >
              Climate Risks
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="Policy" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {policyUpdates.length > 0 ? (
                policyUpdates.map((update, index) => (
                  <UpdateCard key={index} update={update} formatDate={formatDate} />
                ))
              ) : (
                <div className="col-span-3 p-6 text-center">
                  <p className="text-muted-foreground">No policy updates available at this time.</p>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="Technology & Innovation" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {techInnovationUpdates.length > 0 ? (
                techInnovationUpdates.map((update, index) => (
                  <UpdateCard key={index} update={update} formatDate={formatDate} />
                ))
              ) : (
                <div className="col-span-3 p-6 text-center">
                  <p className="text-muted-foreground">No technology & innovation updates available at this time.</p>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="Climate Risks" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {climateRiskUpdates.length > 0 ? (
                climateRiskUpdates.map((update, index) => (
                  <UpdateCard key={index} update={update} formatDate={formatDate} />
                ))
              ) : (
                <div className="col-span-3 p-6 text-center">
                  <p className="text-muted-foreground">No climate risk updates available at this time.</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

// Extracted UpdateCard component for cleaner code
interface UpdateCardProps {
  update: SectorUpdate;
  formatDate: (date: string) => string;
}

const UpdateCard: React.FC<UpdateCardProps> = ({ update, formatDate }) => (
  <Card className="hover:shadow-md transition-shadow">
    <CardHeader className="pb-2">
      <div className="flex justify-between">
        <Badge>{update.update_type}</Badge>
        <span className="text-xs text-muted-foreground">{formatDate(update.update_date)}</span>
      </div>
      <CardTitle className="text-lg">{update.title}</CardTitle>
    </CardHeader>
    <CardContent>
      <p className="text-sm text-muted-foreground mb-4">
        {update.snippet}
      </p>
      <a href={update.url} target="_blank" rel="noopener noreferrer" className="text-sm text-recrea-turquoise flex items-center gap-1 hover:underline">
        <Link className="h-3 w-3" />
        Read more
      </a>
    </CardContent>
  </Card>
);

interface PerformanceCardProps {
  title: string;
  value: number;
  color: string;
  showTotal?: boolean;
  decreasing?: boolean;
}

const PerformanceCard: React.FC<PerformanceCardProps> = ({
  title,
  value,
  color,
  showTotal = false,
  decreasing = false,
}) => (
  <Card className="hover:shadow-md transition-shadow">
    <CardHeader className="pb-2">
      <CardTitle className="text-lg">{title}</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="flex items-end gap-2">
        <span className={`text-3xl font-bold ${color}`}>{value}%</span>
        {showTotal ? (
          <span className="text-sm text-muted-foreground">of total</span>
        ) : (
          <span className="text-sm text-green-500 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={decreasing ? "M19 14l-7 7m0 0l-7-7m7 7V3" : "M5 10l7-7m0 0l7 7m-7-7v18"} />
            </svg>
            YoY
          </span>
        )}
      </div>
      <p className="text-sm text-muted-foreground mt-2">
        Industry average {title.toLowerCase()}
      </p>
    </CardContent>
  </Card>
);

export default PerformanceTab;
