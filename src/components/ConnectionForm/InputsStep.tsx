
import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus, Info } from "lucide-react";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";

import {
  FormData,
  techs,
  energySources,
  emissions,
  materials,
  units,
  EnergyInput,
  EmissionInput,
  MaterialInput,
  TechnologyResponse,
  MaterialResponse,
  EnergyResponse,
  EmissionResponse
} from "./types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { filterValidNodes, getNodeLabel } from "@/utils/dropdownUtils";
import { fetchActivityNameTechnologies } from '@/services/connectionFormApi';
import { useToastContext } from '@/contexts/ToastContext';
import { AddResourceDropdownOption } from './AddResourceDropdownOption';

interface InputsStepProps {
  formData: FormData;
  updateField: (field: string, value: string | any[]) => void;
  updateFormField: (section: string, field: string, value: string) => void;
  errors: Record<string, string>;
  usingManualEntry: boolean;
  energyInputAutoFillLabel: string;
  matInputAutoFillLabel: string;
  technologyAutoFillLabel: string;
  availableTechnologies: string[];
  availableNodes?: any[];
  technologies: string[];
  activeTechnology: string;
  setActiveTechnology: (tech: string) => void;
  onAddTechnology?: (customTechName?: string) => void;
  updateTechnologyName: (oldTechName: string, newTechName: string) => void;
  readOnly?: boolean;
  // API data props
  apiTechnologies?: TechnologyResponse[];
  apiMaterials?: MaterialResponse[];
  apiEnergies?: EnergyResponse[];
  apiEmissions?: EmissionResponse[];
  isLoadingApiData?: boolean;
  onCreateTechnology?: (name: string, description?: string) => Promise<TechnologyResponse | null>;
  sectorUuid?: string;
  onMaterialAdded?: (material: MaterialResponse) => void;
  onEnergyAdded?: (energy: EnergyResponse) => void;
  onEmissionAdded?: (emission: EmissionResponse) => void;
}

export const InputsStep: React.FC<InputsStepProps> = ({
  formData,
  updateField,
  updateFormField,
  errors,
  usingManualEntry,
  energyInputAutoFillLabel,
  matInputAutoFillLabel,
  technologyAutoFillLabel,
  availableTechnologies = [],
  availableNodes = [],
  technologies,
  activeTechnology,
  setActiveTechnology,
  onAddTechnology,
  updateTechnologyName,
  readOnly = false,
  apiTechnologies = [],
  apiMaterials = [],
  apiEnergies = [],
  apiEmissions = [],
  isLoadingApiData = false,
  onCreateTechnology,
  sectorUuid,
  onMaterialAdded,
  onEnergyAdded,
  onEmissionAdded
}) => {
  const { toast } = useToastContext();

  // State for managing add resource dialogs
  const [showMaterialDialog, setShowMaterialDialog] = useState(false);
  const [showEnergyDialog, setShowEnergyDialog] = useState(false);
  const [showEmissionDialog, setShowEmissionDialog] = useState(false);
  const [currentMaterialInputId, setCurrentMaterialInputId] = useState<string | null>(null);
  const [currentEnergyInputId, setCurrentEnergyInputId] = useState<string | null>(null);
  const [currentEmissionInputId, setCurrentEmissionInputId] = useState<string | null>(null);

  // State to store technologies for each source activity
  const [sourceActivityTechnologies, setSourceActivityTechnologies] = useState<Record<string, TechnologyResponse[]>>({});
  const [loadingSourceTechnologies, setLoadingSourceTechnologies] = useState<Record<string, boolean>>({});

  // State for custom technology handling
  const [showCustomTechInput, setShowCustomTechInput] = useState(false);
  const [customTechnology, setCustomTechnology] = useState("");

  // Initialize state for dynamic inputs
  const [energyInputs, setEnergyInputs] = useState<EnergyInput[]>([]);
  const [emissionInputs, setEmissionInputs] = useState<EmissionInput[]>([]);
  const [materialInputs, setMaterialInputs] = useState<MaterialInput[]>([]);

  // Flag to track if we're loading data (to prevent sync during load)
  const [isLoadingData, setIsLoadingData] = useState(false);

  // Function to fetch technologies for a specific source activity
  const fetchTechnologiesForSourceActivity = async (sourceActivityNodeId: string) => {
    if (!sourceActivityNodeId || sourceActivityNodeId === 'Nil') {
      return [];
    }

    // Convert node ID to activity name
    const activityName = getActivityNameFromId(sourceActivityNodeId);
    if (!activityName || activityName === 'Nil') {
      return [];
    }

    // Check if we already have the technologies cached
    if (sourceActivityTechnologies[sourceActivityNodeId]) {
      return sourceActivityTechnologies[sourceActivityNodeId];
    }

    // Check if we're already loading this activity's technologies
    if (loadingSourceTechnologies[sourceActivityNodeId]) {
      return [];
    }

    try {
      setLoadingSourceTechnologies(prev => ({ ...prev, [sourceActivityNodeId]: true }));
      // Use the activity name for the API call, now with sectorUuid for UUID-based fetching
      const technologies = await fetchActivityNameTechnologies(activityName, { toast }, sectorUuid);

      setSourceActivityTechnologies(prev => ({
        ...prev,
        [sourceActivityNodeId]: technologies
      }));

      return technologies;
    } catch (error) {
      console.error(`Error fetching technologies for source activity ${activityName} (node: ${sourceActivityNodeId}):`, error);
      return [];
    } finally {
      setLoadingSourceTechnologies(prev => ({ ...prev, [sourceActivityNodeId]: false }));
    }
  };

  // Update local state when formData changes (technology switching)
  useEffect(() => {
    setIsLoadingData(true);

    // Update energy inputs from formData
    if (formData.energyInputs && formData.energyInputs.length > 0) {
      setEnergyInputs(formData.energyInputs);
    } else {
      // Initialize with one empty entry if no data
      setEnergyInputs([{
        id: `energy-${Date.now()}`,
        source: formData.energyInput?.source || "",
        unit: formData.energyInput?.unit || "GJ",
        cost: formData.energyInput?.cost || "",
        sec: formData.energyInput?.sec || "",
        sourceActivity: "Nil",
        technology: "Nil"
      }]);
    }

    // Update emission inputs from formData
    if (formData.emissions && formData.emissions.length > 0) {
      setEmissionInputs(formData.emissions);
    } else {
      // Initialize with one empty entry if no data
      setEmissionInputs([{
        id: `emission-${Date.now()}`,
        source: formData.emission?.source || "",
        factor: formData.emission?.ef || "",
        unit: formData.emission?.unit || "kg"
      }]);
    }

    // Update material inputs from formData
    if (formData.materialInputs && formData.materialInputs.length > 0) {
      setMaterialInputs(formData.materialInputs);
    } else {
      // Initialize with one empty entry if no data
      setMaterialInputs([{
        id: `material-${Date.now()}`,
        material: formData.matInput?.material || "",
        unit: formData.matInput?.unit || "Tonnes",
        cost: formData.matInput?.cost || "",
        smc: formData.matInput?.smc || "",
        sourceActivity: "Nil",
        technology: "Nil"
      }]);
    }

    // Set loading to false after a brief delay to allow state to settle
    setTimeout(() => setIsLoadingData(false), 100);
  }, [formData, activeTechnology]);

  // Sync local state changes back to parent technology form data
  // Only sync when user is making changes (not when loading data)
  useEffect(() => {
    if (!isLoadingData && energyInputs.length > 0) {
      updateField('energyInputs', energyInputs);
    }
  }, [energyInputs, isLoadingData]);

  useEffect(() => {
    if (!isLoadingData && emissionInputs.length > 0) {
      updateField('emissions', emissionInputs);
    }
  }, [emissionInputs, isLoadingData]);

  useEffect(() => {
    if (!isLoadingData && materialInputs.length > 0) {
      updateField('materialInputs', materialInputs);
    }
  }, [materialInputs, isLoadingData]);

  // Pre-fetch technologies for existing source activities
  useEffect(() => {
    const sourceActivities = new Set<string>();

    // Collect all source activities from energy inputs
    energyInputs.forEach(input => {
      if (input.sourceActivity && input.sourceActivity !== 'Nil') {
        sourceActivities.add(input.sourceActivity);
      }
    });

    // Collect all source activities from material inputs
    materialInputs.forEach(input => {
      if (input.sourceActivity && input.sourceActivity !== 'Nil') {
        sourceActivities.add(input.sourceActivity);
      }
    });

    // Fetch technologies for each unique source activity
    sourceActivities.forEach(sourceActivity => {
      fetchTechnologiesForSourceActivity(sourceActivity);
    });
  }, [energyInputs, materialInputs]);

  // Filter available nodes and get all technologies
  const validNodes = filterValidNodes(availableNodes);

  // Helper function to convert node ID to human-readable activity name
  const getActivityNameFromId = (nodeId: string): string => {
    if (!nodeId || nodeId === "Nil") return "Nil";

    // Find the node in availableNodes
    const node = availableNodes.find(n => n.id === nodeId);
    if (node) {
      return getNodeLabel(node);
    }

    // If not found, return the ID as fallback (this handles the case where it's already a name)
    return nodeId;
  };

  // Combine API technologies with fallback technologies
  const allTechnologies = apiTechnologies.length > 0
    ? apiTechnologies.map(tech => tech.title || tech.name).filter(name => name && name.trim() !== "")
    : [...new Set([...availableTechnologies, ...techs])].filter(tech => tech && tech.trim() !== "");

  // Get materials, energies, and emissions from API or fallback
  // CRITICAL FIX: Include materials from current form data to ensure base scenario materials are available
  const baseMaterials = apiMaterials.length > 0
    ? apiMaterials.map(material => material.name).filter(name => name && name.trim() !== "")
    : materials.filter(material => material && material.trim() !== "");

  const formDataMaterials = materialInputs
    .map(input => input.material)
    .filter(material => material && material.trim() !== "");

  const availableMaterials = [...new Set([...baseMaterials, ...formDataMaterials])].filter(Boolean);

  // CRITICAL FIX: Include energies from current form data to ensure base scenario energies are available
  const baseEnergies = apiEnergies.length > 0
    ? apiEnergies.map(energy => energy.name).filter(name => name && name.trim() !== "")
    : energySources.filter(energy => energy && energy.trim() !== "");

  const formDataEnergies = energyInputs
    .map(input => input.source)
    .filter(source => source && source.trim() !== "");

  const availableEnergies = [...new Set([...baseEnergies, ...formDataEnergies])].filter(Boolean);

  const availableEmissions = apiEmissions.length > 0
    ? apiEmissions.map(emission => emission.name).filter(name => name && name.trim() !== "")
    : emissions.filter(emission => emission && emission.trim() !== "");

  // Technology handling functions
  const handleTechnologyChange = async (value: string) => {
    if (value === "add_custom") {
      setShowCustomTechInput(true);
    } else if (value) {
      updateField('technology', value);
      setShowCustomTechInput(false);
      // Update the technology name in the tab header
      updateTechnologyName(activeTechnology, value);
    }
  };

  const handleCustomTechnologySubmit = async () => {
    if (customTechnology.trim()) {
      const newTech = customTechnology.trim();

      // Try to create technology via API if available
      if (onCreateTechnology) {
        const createdTech = await onCreateTechnology(newTech);
        if (createdTech) {
          updateField('technology', createdTech.title || createdTech.name);
          updateTechnologyName(activeTechnology, createdTech.title || createdTech.name);
        } else {
          // Fallback to local creation if API fails
          updateField('technology', newTech);
          updateTechnologyName(activeTechnology, newTech);
        }
      } else {
        // Fallback to local creation if no API handler
        updateField('technology', newTech);
        updateTechnologyName(activeTechnology, newTech);
      }

      setCustomTechnology("");
      setShowCustomTechInput(false);
    }
  };



  // Handler for adding new energy input
  const addEnergyInput = () => {
    const newEnergyInput = {
      id: `energy-${Date.now()}`,
      source: "",
      unit: "GJ",
      cost: "",
      sec: "",
      sourceActivity: "Nil",
      technology: "Nil"
    };
    setEnergyInputs([...energyInputs, newEnergyInput]);
  };

  // Handler for adding new emission input
  const addEmissionInput = () => {
    const newEmissionInput = {
      id: `emission-${Date.now()}`,
      source: "",
      factor: "",
      unit: "GJ"
    };
    setEmissionInputs([...emissionInputs, newEmissionInput]);
  };

  // Handler for adding new material input
  const addMaterialInput = () => {
    const newMaterialInput = {
      id: `material-${Date.now()}`,
      material: "",
      unit: "Tonnes",
      cost: "",
      smc: "",
      sourceActivity: "Nil",
      technology: "Nil"
    };
    setMaterialInputs([...materialInputs, newMaterialInput]);
  };

  // Handler for updating energy input
  const updateEnergyInput = (id: string, field: string, value: string) => {
    setEnergyInputs(prevInputs =>
      prevInputs.map(input => {
        if (input.id === id) {
          const updatedInput = { ...input, [field]: value };
          // If source activity is set to "Nil", automatically set technology to "Nil"
          if (field === 'sourceActivity' && value === 'Nil') {
            updatedInput.technology = 'Nil';
          }
          // If source activity changes from "Nil" to something else, reset technology if it was "Nil"
          else if (field === 'sourceActivity' && value !== 'Nil' && input.technology === 'Nil') {
            updatedInput.technology = '';
          }
          return updatedInput;
        }
        return input;
      })
    );

    // If source activity changed, fetch technologies for the new source activity
    if (field === 'sourceActivity' && value !== 'Nil') {
      fetchTechnologiesForSourceActivity(value);
    }
  };

  // Handler for updating emission input
  const updateEmissionInput = (id: string, field: string, value: string) => {
    setEmissionInputs(prevInputs => 
      prevInputs.map(input => 
        input.id === id ? { ...input, [field]: value } : input
      )
    );
  };

  // Handler for updating material input
  const updateMaterialInput = (id: string, field: string, value: string) => {
    setMaterialInputs(prevInputs =>
      prevInputs.map(input => {
        if (input.id === id) {
          const updatedInput = { ...input, [field]: value };
          // If source activity is set to "Nil", automatically set technology to "Nil"
          if (field === 'sourceActivity' && value === 'Nil') {
            updatedInput.technology = 'Nil';
          }
          // If source activity changes from "Nil" to something else, reset technology if it was "Nil"
          else if (field === 'sourceActivity' && value !== 'Nil' && input.technology === 'Nil') {
            updatedInput.technology = '';
          }
          return updatedInput;
        }
        return input;
      })
    );

    // If source activity changed, fetch technologies for the new source activity
    if (field === 'sourceActivity' && value !== 'Nil') {
      fetchTechnologiesForSourceActivity(value);
    }
  };

  // Helper function to get technologies for a specific source activity
  const getTechnologiesForSourceActivity = (sourceActivityNodeId: string): string[] => {
    if (!sourceActivityNodeId || sourceActivityNodeId === 'Nil') {
      return ['Nil'];
    }

    // Get technologies from cache
    const cachedTechnologies = sourceActivityTechnologies[sourceActivityNodeId];
    if (cachedTechnologies && cachedTechnologies.length > 0) {
      return cachedTechnologies.map(tech => tech.title || tech.name).filter(name => name && name.trim() !== "");
    }

    // If no cached technologies and not loading, return fallback message
    if (!loadingSourceTechnologies[sourceActivityNodeId]) {
      return ['No technologies available'];
    }

    // If loading or no data yet, return empty array
    return [];
  };



  return (
    <div className={readOnly ? "opacity-75" : ""}>
      {/* Technology management section */}
      {!readOnly && (
        <div className="mb-4 flex items-center justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => onAddTechnology && onAddTechnology()}
            className="flex items-center gap-1 text-sm"
            disabled={readOnly}
          >
            <Plus className="h-4 w-4" /> Add Technology
          </Button>
        </div>
      )}

      {/* Technology tabs - only show if multiple technologies exist */}
      {technologies.length > 1 ? (
        <Tabs value={activeTechnology} onValueChange={readOnly ? undefined : setActiveTechnology} className="w-full">
          <TabsList className="mb-2 w-full overflow-x-auto flex whitespace-nowrap">
            {technologies.map((tech) => (
              <TabsTrigger
                key={tech}
                value={tech}
                className={`min-w-[100px] flex-shrink-0 ${readOnly ? 'cursor-not-allowed opacity-70' : ''}`}
                disabled={readOnly}
              >
                {tech}
              </TabsTrigger>
            ))}
          </TabsList>

          {technologies.map((tech) => (
            <TabsContent key={tech} value={tech}>
              {renderTechnologyContent(tech)}
            </TabsContent>
          ))}
        </Tabs>
      ) : (
        /* Single technology - render content directly */
        renderTechnologyContent(activeTechnology)
      )}
    </div>
  );

  function renderTechnologyContent(technology: string) {
    return (
      <>
        {/* Technology Selection Field */}
        <div className="border-2 border-orange-300 rounded-md mb-3 p-3">
          <div className="font-semibold mb-1">Technology</div>
          <div className="flex flex-col gap-2">
            {!showCustomTechInput ? (
              <>
                {/* Technology and Life of Technology section - aligned */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Technology dropdown */}
                  <div className="flex flex-col justify-end">
                    <label className="block text-sm font-medium mb-1 text-gray-600">Select Technology</label>
                    {readOnly ? (
                      <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300 h-10 flex items-center">
                        {activeTechnology || "Select technology"}
                      </div>
                    ) : (
                      <Select
                        value={activeTechnology || undefined}
                        onValueChange={readOnly ? undefined : handleTechnologyChange}
                        disabled={readOnly}
                      >
                        <SelectTrigger className="w-full h-10">
                          <SelectValue placeholder="Select technology" />
                        </SelectTrigger>
                        <SelectContent>
                          {isLoadingApiData ? (
                            <SelectItem value="loading" disabled>Loading technologies...</SelectItem>
                          ) : (
                            <>
                              {allTechnologies.map(tech => (
                                <SelectItem key={tech} value={tech}>{tech}</SelectItem>
                              ))}
                              <SelectItem value="add_custom">+ Add new technology</SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    )}
                  </div>

                  {/* Life of Technology section */}
                  <div className="flex flex-col">
                    <div className="mb-2">
                      <span className="text-sm font-medium text-gray-600">Life of Technology</span>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {/* Start Year dropdown */}
                      <div className="flex flex-col">
                        <label className="block text-sm font-medium mb-1 text-gray-600">Start Year</label>
                        {readOnly ? (
                          <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300 h-10 flex items-center">
                            {formData.startYear || "2000"}
                          </div>
                        ) : (
                          <Select
                            value={formData.startYear || "2000"}
                            onValueChange={(value) => updateField('startYear', value)}
                            disabled={readOnly}
                          >
                            <SelectTrigger className="w-full h-10">
                              <SelectValue placeholder="Start Year" />
                            </SelectTrigger>
                            <SelectContent>
                              {Array.from({ length: 76 }, (_, i) => 2000 + i).map(year => (
                                <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      </div>

                      {/* End Year dropdown */}
                      <div className="flex flex-col">
                        <label className="block text-sm font-medium mb-1 text-gray-600">End Year</label>
                        {readOnly ? (
                          <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300 h-10 flex items-center">
                            {formData.endYear || "2075"}
                          </div>
                        ) : (
                          <Select
                            value={formData.endYear || "2075"}
                            onValueChange={(value) => updateField('endYear', value)}
                            disabled={readOnly}
                          >
                            <SelectTrigger className="w-full h-10">
                              <SelectValue placeholder="End Year" />
                            </SelectTrigger>
                            <SelectContent>
                              {Array.from({ length: 76 }, (_, i) => 2000 + i).map(year => (
                                <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Technology Emission Section */}
                <div className="mt-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="font-semibold">Emission</div>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-3 w-3 text-gray-400 cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Emission per unit Capacity of the technology</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <div className="flex-1 min-w-0">
                    <Input
                      className="text-sm"
                      value={formData.technologyEmission || ""}
                      onChange={(e) => updateField('technologyEmission', e.target.value)}
                      disabled={readOnly}
                      placeholder="Enter emission value"
                    />
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 min-w-0">
                <label className="block text-sm font-medium mb-1">Enter new technology name</label>
                <div className="flex gap-2">
                  <Input
                    value={customTechnology}
                    onChange={(e) => setCustomTechnology(e.target.value)}
                    placeholder="Type new technology name"
                    className="flex-1"
                    disabled={readOnly}
                  />
                  <Button
                    onClick={handleCustomTechnologySubmit}
                    type="button"
                    disabled={customTechnology.trim() === "" || readOnly}
                  >
                    Add
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowCustomTechInput(false)}
                    type="button"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Combined Energy Input and Emissions Section */}
      <div className="border-2 border-blue-300 rounded-md mb-3 p-3">
        <div className="flex items-center justify-between mb-1">
          <div className="font-semibold">Energy Input</div>
          {!readOnly && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addEnergyInput}
              className="h-6 px-2 text-xs"
              disabled={readOnly}
            >
              <Plus className="h-3 w-3 mr-1" />
              Energy Input
            </Button>
          )}
        </div>

        {/* Render dynamic energy inputs */}
        {energyInputs.map((energyInput, index) => (
          <div 
            key={energyInput.id} 
            className={`${index > 0 ? 'mt-3 pt-3 border-t border-gray-200' : ''}`}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mb-3">
              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Name</label>
                {index === 0 && energyInputAutoFillLabel ? (
                  <div className="border rounded px-2 py-2 bg-gray-50 text-sm">
                    {energyInputAutoFillLabel}
                  </div>
                ) : (
                  <>
                    <select
                      className="w-full border rounded px-2 py-2 text-sm"
                      value={energyInput.source}
                      onChange={(e) => {
                        if (e.target.value === "__ADD_NEW__") {
                          // Reset the select value and open the dialog
                          e.target.value = energyInput.source;
                          setShowEnergyDialog(true);
                          setCurrentEnergyInputId(energyInput.id);
                          return;
                        }
                        updateEnergyInput(energyInput.id, 'source', e.target.value);
                      }}
                      disabled={isLoadingApiData || readOnly}
                    >
                      <option value="">{isLoadingApiData ? "Loading energies..." : "Select"}</option>
                      {availableEnergies.map(v => <option key={v} value={v}>{v}</option>)}
                      {!readOnly && (
                        <option value="__ADD_NEW__" style={{ fontWeight: 'bold', color: '#2563eb' }}>
                          + Add New Energy
                        </option>
                      )}
                    </select>
                    {showEnergyDialog && (
                      <AddResourceDropdownOption
                        type="energy"
                        onResourceAdded={(energy) => {
                          if (onEnergyAdded) {
                            onEnergyAdded(energy as EnergyResponse);
                          }
                          // Set the newly created energy as selected
                          if (currentEnergyInputId) {
                            updateEnergyInput(currentEnergyInputId, 'source', energy.name);
                          }
                          setShowEnergyDialog(false);
                          setCurrentEnergyInputId(null);
                        }}
                        disabled={isLoadingApiData}
                      />
                    )}
                  </>
                )}
              </div>

              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Unit</label>
                <select 
                  className="w-full border rounded px-2 py-2 text-sm"
                  value={energyInput.unit}
                  onChange={(e) => updateEnergyInput(energyInput.id, 'unit', e.target.value)}
                  disabled={readOnly}
                >
                  {units.map(v => <option key={v} value={v}>{v}</option>)}
                </select>
              </div>
              
              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Cost/Unit</label>
                <Input
                  className="text-sm"
                  value={energyInput.cost}
                  onChange={(e) => updateEnergyInput(energyInput.id, 'cost', e.target.value)}
                  disabled={readOnly}
                />
              </div>

              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">SEC</label>
                <Input 
                  className="text-sm"
                  value={energyInput.sec}
                  onChange={(e) => updateEnergyInput(energyInput.id, 'sec', e.target.value)}
                  disabled={readOnly}
                />
              </div>
            </div>

            {/* Source Activity and Technology dropdowns moved below */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Source Activity</label>
                {readOnly ? (
                  <div className="w-full border rounded px-2 py-2 text-sm bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {getActivityNameFromId(energyInput.sourceActivity || "Nil")}
                  </div>
                ) : (
                  <Select
                    value={energyInput.sourceActivity || "Nil"}
                    onValueChange={(value) => updateEnergyInput(energyInput.id, 'sourceActivity', value)}
                    disabled={readOnly}
                  >
                    <SelectTrigger className="w-full text-sm">
                      <SelectValue placeholder="Select source" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Nil">Nil</SelectItem>
                      {validNodes.map(node => (
                        <SelectItem key={node.id} value={node.id}>
                          {getNodeLabel(node)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>

              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Select Technology</label>
                {readOnly ? (
                  <div className="w-full border rounded px-2 py-2 text-sm bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {energyInput.technology || "Nil"}
                  </div>
                ) : (
                  <Select
                    value={energyInput.technology || "Nil"}
                    onValueChange={(value) => updateEnergyInput(energyInput.id, 'technology', value)}
                    onOpenChange={(open) => {
                      if (open && energyInput.sourceActivity && energyInput.sourceActivity !== "Nil") {
                        fetchTechnologiesForSourceActivity(energyInput.sourceActivity);
                      }
                    }}
                    disabled={energyInput.sourceActivity === "Nil" || readOnly}
                  >
                    <SelectTrigger className="w-full text-sm">
                      <SelectValue placeholder="Select technology" />
                    </SelectTrigger>
                    <SelectContent>
                      {energyInput.sourceActivity === "Nil" && (
                        <SelectItem value="Nil">Nil</SelectItem>
                      )}
                      {loadingSourceTechnologies[energyInput.sourceActivity] ? (
                        <SelectItem value="loading" disabled>Loading technologies...</SelectItem>
                      ) : (() => {
                        const technologies = getTechnologiesForSourceActivity(energyInput.sourceActivity).filter(tech => tech !== "Nil");

                        if (technologies.length === 0) {
                          return <SelectItem value="no-tech" disabled>No technologies available</SelectItem>;
                        }

                        return technologies.map(tech => (
                          <SelectItem
                            key={tech}
                            value={tech}
                            disabled={tech === "No technologies available"}
                          >
                            {tech}
                          </SelectItem>
                        ));
                      })()}
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>

            {/* Lower and Upper Bounds */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium">Lower Bound (%)</label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3 w-3 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Min percentage of this energy allowed in the current energy mix</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  className="text-sm"
                  type="number"
                  min="0"
                  max="100"
                  value={energyInput.lowerBound || ""}
                  onChange={(e) => updateEnergyInput(energyInput.id, 'lowerBound', e.target.value)}
                  disabled={readOnly}
                  placeholder="0"
                />
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium">Upper Bound (%)</label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3 w-3 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Max percentage of this energy allowed in the current energy mix</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  className="text-sm"
                  type="number"
                  min="0"
                  max="100"
                  value={energyInput.upperBound || ""}
                  onChange={(e) => updateEnergyInput(energyInput.id, 'upperBound', e.target.value)}
                  disabled={readOnly}
                  placeholder="100"
                />
              </div>
            </div>
          </div>
        ))}

        {/* Emissions Section - now part of the same block */}
        <div className="flex items-center justify-between mt-4 mb-1">
          <div className="font-semibold">Emissions</div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addEmissionInput}
            className="h-6 px-2 text-xs"
            disabled={readOnly}
          >
            <Plus className="h-3 w-3 mr-1" />
            Emission
          </Button>
        </div>

        {/* Render dynamic emission inputs */}
        {emissionInputs.map((emission, index) => (
          <div 
            key={emission.id} 
            className={`${index > 0 ? 'mt-3 pt-3 border-t border-gray-200' : ''}`}
          >
            <div className="flex flex-col md:flex-row gap-2">
              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Name</label>
                <>
                  <select
                    className="w-full border rounded px-2 py-2 text-sm"
                    value={emission.source}
                    onChange={(e) => {
                      if (e.target.value === "__ADD_NEW__") {
                        // Reset the select value and open the dialog
                        e.target.value = emission.source;
                        setShowEmissionDialog(true);
                        setCurrentEmissionInputId(emission.id);
                        return;
                      }
                      updateEmissionInput(emission.id, 'source', e.target.value);
                    }}
                    disabled={isLoadingApiData || readOnly}
                  >
                    <option value="">{isLoadingApiData ? "Loading emissions..." : "Select"}</option>
                    {availableEmissions.map(v => <option key={v} value={v}>{v}</option>)}
                    {!readOnly && (
                      <option value="__ADD_NEW__" style={{ fontWeight: 'bold', color: '#2563eb' }}>
                        + Add New Emission
                      </option>
                    )}
                  </select>
                  {showEmissionDialog && (
                    <AddResourceDropdownOption
                      type="emission"
                      onResourceAdded={(newEmission) => {
                        if (onEmissionAdded) {
                          onEmissionAdded(newEmission as EmissionResponse);
                        }
                        // Set the newly created emission as selected
                        if (currentEmissionInputId) {
                          updateEmissionInput(currentEmissionInputId, 'source', newEmission.name);
                        }
                        setShowEmissionDialog(false);
                        setCurrentEmissionInputId(null);
                      }}
                      disabled={isLoadingApiData}
                    />
                  )}
                </>
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-1 mb-1">
                  <label className="text-sm font-medium">Emission Factor</label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3 w-3 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Emissions per unit of energy</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  className="text-sm"
                  value={emission.factor}
                  onChange={(e) => updateEmissionInput(emission.id, 'factor', e.target.value)}
                  disabled={readOnly}
                />
              </div>

              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Unit</label>
                <select 
                  className="w-full border rounded px-2 py-2 text-sm"
                  value={emission.unit}
                  onChange={(e) => updateEmissionInput(emission.id, 'unit', e.target.value)}
                  disabled={readOnly}
                >
                  {units.map(v => <option key={v} value={v}>{v}</option>)}
                </select>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Material Input Section */}
      <div className="border-2 border-gray-200 rounded-md mb-3 p-3">
        <div className="flex items-center justify-between mb-1">
          <div className="font-semibold">Material Input</div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addMaterialInput}
            className="h-6 px-2 text-xs"
            disabled={readOnly}
          >
            <Plus className="h-3 w-3 mr-1" />
            Material Input
          </Button>
        </div>
        
        {/* Render dynamic material inputs */}
        {materialInputs.map((materialInput, index) => (
          <div 
            key={materialInput.id} 
            className={`${index > 0 ? 'mt-3 pt-3 border-t border-gray-200' : ''}`}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mb-3">
              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Name</label>
                {index === 0 && matInputAutoFillLabel ? (
                  <div className="border rounded px-2 py-2 bg-gray-50 text-sm">
                    {matInputAutoFillLabel}
                  </div>
                ) : (
                  <>
                    <select
                      className="w-full border rounded px-2 py-2 text-sm"
                      value={materialInput.material}
                      onChange={(e) => {
                        if (e.target.value === "__ADD_NEW__") {
                          // Reset the select value and open the dialog
                          e.target.value = materialInput.material;
                          setShowMaterialDialog(true);
                          setCurrentMaterialInputId(materialInput.id);
                          return;
                        }
                        updateMaterialInput(materialInput.id, 'material', e.target.value);
                      }}
                      disabled={isLoadingApiData || readOnly}
                    >
                      <option value="">{isLoadingApiData ? "Loading materials..." : "Select"}</option>
                      {availableMaterials.map(v => <option key={v} value={v}>{v}</option>)}
                      {!readOnly && sectorUuid && (
                        <option value="__ADD_NEW__" style={{ fontWeight: 'bold', color: '#2563eb' }}>
                          + Add New Material
                        </option>
                      )}
                    </select>
                    {showMaterialDialog && (
                      <AddResourceDropdownOption
                        type="material"
                        sectorUuid={sectorUuid}
                        onResourceAdded={(material) => {
                          if (onMaterialAdded) {
                            onMaterialAdded(material as MaterialResponse);
                          }
                          // Set the newly created material as selected
                          if (currentMaterialInputId) {
                            updateMaterialInput(currentMaterialInputId, 'material', material.name);
                          }
                          setShowMaterialDialog(false);
                          setCurrentMaterialInputId(null);
                        }}
                        disabled={isLoadingApiData}
                      />
                    )}
                  </>
                )}
              </div>

              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Unit</label>
                <select 
                  className="w-full border rounded px-2 py-2 text-sm"
                  value={materialInput.unit}
                  onChange={(e) => updateMaterialInput(materialInput.id, 'unit', e.target.value)}
                  disabled={readOnly}
                >
                  {units.map(v => <option key={v} value={v}>{v}</option>)}
                </select>
              </div>

              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Cost/Unit</label>
                <Input
                  className="text-sm"
                  value={materialInput.cost}
                  onChange={(e) => updateMaterialInput(materialInput.id, 'cost', e.target.value)}
                  disabled={readOnly}
                />
              </div>

              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">SMC</label>
                <Input 
                  className="text-sm"
                  value={materialInput.smc}
                  onChange={(e) => updateMaterialInput(materialInput.id, 'smc', e.target.value)}
                  disabled={readOnly}
                />
              </div>
            </div>

            {/* Source Activity and Technology dropdowns moved below */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Source Activity</label>
                {readOnly ? (
                  <div className="w-full border rounded px-2 py-2 text-sm bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {getActivityNameFromId(materialInput.sourceActivity || "Nil")}
                  </div>
                ) : (
                  <Select
                    value={materialInput.sourceActivity || "Nil"}
                    onValueChange={(value) => updateMaterialInput(materialInput.id, 'sourceActivity', value)}
                    disabled={readOnly}
                  >
                    <SelectTrigger className="w-full text-sm">
                      <SelectValue placeholder="Select source" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Nil">Nil</SelectItem>
                      {validNodes.map(node => (
                        <SelectItem key={node.id} value={node.id}>
                          {getNodeLabel(node)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>

              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Select Technology</label>
                {readOnly ? (
                  <div className="w-full border rounded px-2 py-2 text-sm bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {materialInput.technology || "Nil"}
                  </div>
                ) : (
                  <Select
                    value={materialInput.technology || "Nil"}
                    onValueChange={(value) => updateMaterialInput(materialInput.id, 'technology', value)}
                    onOpenChange={(open) => {
                      if (open && materialInput.sourceActivity && materialInput.sourceActivity !== "Nil") {
                        fetchTechnologiesForSourceActivity(materialInput.sourceActivity);
                      }
                    }}
                    disabled={materialInput.sourceActivity === "Nil" || readOnly}
                  >
                    <SelectTrigger className="w-full text-sm">
                      <SelectValue placeholder="Select technology" />
                    </SelectTrigger>
                    <SelectContent>
                      {materialInput.sourceActivity === "Nil" && (
                        <SelectItem value="Nil">Nil</SelectItem>
                      )}
                      {loadingSourceTechnologies[materialInput.sourceActivity] ? (
                        <SelectItem value="loading" disabled>Loading technologies...</SelectItem>
                      ) : (() => {
                        const technologies = getTechnologiesForSourceActivity(materialInput.sourceActivity).filter(tech => tech !== "Nil");

                        if (technologies.length === 0) {
                          return <SelectItem value="no-tech" disabled>No technologies available</SelectItem>;
                        }

                        return technologies.map(tech => (
                          <SelectItem
                            key={tech}
                            value={tech}
                            disabled={tech === "No technologies available"}
                          >
                            {tech}
                          </SelectItem>
                        ));
                      })()}
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>

            {/* Lower and Upper Bounds */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium">Lower Bound (%)</label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3 w-3 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Min percentage of this material allowed in the current energy mix</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  className="text-sm"
                  type="number"
                  min="0"
                  max="100"
                  value={materialInput.lowerBound || ""}
                  onChange={(e) => updateMaterialInput(materialInput.id, 'lowerBound', e.target.value)}
                  disabled={readOnly}
                  placeholder="0"
                />
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium">Upper Bound (%)</label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3 w-3 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Max percentage of this material allowed in the current energy mix</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  className="text-sm"
                  type="number"
                  min="0"
                  max="100"
                  value={materialInput.upperBound || ""}
                  onChange={(e) => updateMaterialInput(materialInput.id, 'upperBound', e.target.value)}
                  disabled={readOnly}
                  placeholder="100"
                />
              </div>
            </div>
          </div>
        ))}
      </div>
      </>
    );
  }
};
