import React, { useState, useEffect } from 'react';
import { Plus } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import {
  createMaterial,
  createEnergy,
  createEmission,
  associateMaterialWithSector,
} from '@/services/connectionFormApi';
import { MaterialResponse, EnergyResponse, EmissionResponse } from './types';

interface AddResourceDropdownOptionProps {
  type: 'material' | 'energy' | 'emission';
  sectorUuid?: string; // Required for materials, not needed for energy/emissions
  onResourceAdded: (resource: MaterialResponse | EnergyResponse | EmissionResponse) => void;
  disabled?: boolean;
}

export const AddResourceDropdownOption: React.FC<AddResourceDropdownOptionProps> = ({
  type,
  sectorUuid,
  onResourceAdded,
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [name, setName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Automatically open dialog when component is rendered
  useEffect(() => {
    setIsOpen(true);
  }, []);

  const getResourceLabel = () => {
    switch (type) {
      case 'material': return 'Material';
      case 'energy': return 'Energy';
      case 'emission': return 'Emission';
      default: return 'Resource';
    }
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim()) {
      toast({
        title: "Validation Error",
        description: `Please enter a ${getResourceLabel().toLowerCase()} name`,
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    try {
      let newResource: MaterialResponse | EnergyResponse | EmissionResponse | null = null;

      // Create the resource based on type
      switch (type) {
        case 'material':
          newResource = await createMaterial({ name: name.trim() }, { toast });
          if (newResource && sectorUuid) {
            // Associate material with sector
            const associated = await associateMaterialWithSector(
              sectorUuid, 
              newResource.uuid, 
              { toast }
            );
            if (!associated) {
              // If association failed, still return the created material
              // but show a warning
              toast({
                title: "Warning",
                description: "Material created but failed to associate with sector",
                variant: "destructive"
              });
            }
          }
          break;
        
        case 'energy':
          newResource = await createEnergy({ name: name.trim() }, { toast });
          break;
        
        case 'emission':
          newResource = await createEmission({ name: name.trim() }, { toast });
          break;
      }

      if (newResource) {
        onResourceAdded(newResource);
        setName('');
        setIsOpen(false);
      }
    } catch (error) {
      console.error(`Error creating ${type}:`, error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setName('');
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Add New {getResourceLabel()}</DialogTitle>
            <DialogDescription>
              Create a new {getResourceLabel().toLowerCase()} that will be available in the dropdown.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3"
                placeholder={`Enter ${getResourceLabel().toLowerCase()} name`}
                disabled={isLoading}
                autoFocus
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Creating...' : `Create ${getResourceLabel()}`}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
