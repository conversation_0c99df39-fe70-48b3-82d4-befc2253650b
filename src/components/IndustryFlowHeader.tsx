import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft, HelpCircle, User } from 'lucide-react';
import { useTheme } from '@/components/ThemeProvider';
import Logo from '@/components/Logo';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface IndustryFlowHeaderProps {
  sectorName: string;
  flowTypeLabel: string;
  scenarioName?: string;
  isScenarioMode?: boolean;
  // Navigation props
  onExitScenario?: () => void;
}

export const IndustryFlowHeader: React.FC<IndustryFlowHeaderProps> = ({
  sectorName,
  flowTypeLabel,
  scenarioName,
  isScenarioMode = false,
  onExitScenario
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { theme } = useTheme();
  const [isLoggedIn] = useState<boolean>(true); // Always show profile icon

  // Use startsWith for optimizer highlighting, strict match for dashboard/knowledge
  const isOptimizerTab = location.pathname.startsWith('/optimizer') || location.pathname.startsWith('/industry-flow');
  const isDashboard = location.pathname === '/dashboard';

  return (
    <div className="w-full">
      {/* Main Header - Replicating the Header component structure */}
      <header className={`w-full py-6 px-4 md:px-8 border-b ${theme === 'dark' ? 'border-recrea-card-border' : 'border-recrea-mint'}`}>
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <Logo className="animate-fade-in" />

          <div className="flex items-center gap-6">
            {/* Navigation Items with Tooltips */}
            <nav className="hidden md:flex items-center space-x-6">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={() => navigate('/dashboard')}
                      variant="link"
                      className={`
                        font-medium
                        ${isDashboard
                          ? 'text-recrea-turquoise border-b-2 border-recrea-turquoise font-bold animate-fade-in'
                          : (theme === 'dark' ? 'text-white' : 'text-recrea-dark')}
                        hover:text-recrea-turquoise hover:bg-transparent
                        pb-1 flex items-center gap-1
                      `}
                    >
                      Knowledge Hub
                      <HelpCircle className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs">
                    <p>Get curated best in class knowledge on climate risk for your sector.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={() => navigate('/optimizer')}
                      variant="link"
                      className={`
                        font-medium
                        ${isOptimizerTab
                          ? 'text-recrea-teal border-b-2 border-recrea-teal font-bold animate-fade-in'
                          : (theme === 'dark' ? 'text-white' : 'text-recrea-dark')}
                        hover:text-recrea-teal hover:bg-transparent
                        transition-all
                        flex items-center gap-1
                      `}
                      style={{
                        transition: 'border-color 0.2s, color 0.2s, font-weight 0.2s',
                      }}
                    >
                      Optimizer
                      <HelpCircle className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs">
                    <p>Use our patented AI solution AIde™ to arrive at financial risk under alternate scenarios.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={() => navigate('/marketplace')}
                      variant="link"
                      className={`font-medium ${theme === 'dark' ?
                        'text-white' :
                        'text-recrea-dark'} hover:text-recrea-turquoise hover:bg-transparent flex items-center gap-1`}
                    >
                      Marketplace
                      <HelpCircle className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs">
                    <p>Connect with industry experts and technology providers to facilitate implementation.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </nav>

            {/* Profile Dropdown */}
            {isLoggedIn && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="rounded-full">
                    <User className="h-5 w-5" />
                    <span className="sr-only">User menu</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => navigate('/profile')}>
                    Profile
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate('/settings')}>
                    Settings
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => {
                    localStorage.removeItem('accessToken');
                    navigate('/login');
                  }}>
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </header>

      {/* Industry Flow Specific Header */}
      <div className="flex items-center justify-between pt-8 pb-4 px-12 border-b border-gray-100" style={{ minHeight: 72 }}>
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              // If in scenario mode and onExitScenario is provided, use it to return to inventory
              if (isScenarioMode && onExitScenario) {
                onExitScenario();
              } else {
                // Otherwise, navigate to optimizer home
                navigate('/optimizer');
              }
            }}
            className="hover:bg-gray-100 rounded-lg"
          >
            <span className="sr-only">Back</span>
            <ArrowLeft size={20} className="text-gray-600" />
          </Button>
          <div className="flex flex-col">
            <div className="text-sm text-gray-500 font-medium uppercase tracking-wide">
              {sectorName} Sector
            </div>
            <div className="text-xl font-bold text-gray-900" style={{ letterSpacing: '-0.5px' }}>
              {scenarioName || flowTypeLabel}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className={`px-3 py-1.5 rounded-full text-sm font-medium ${
            isScenarioMode
              ? 'bg-blue-100 text-blue-700 border border-blue-200'
              : 'bg-green-100 text-green-700 border border-green-200'
          }`}>
            {isScenarioMode ? 'Scenario' : 'Inventory'}
          </div>
        </div>
      </div>
    </div>
  );
};
