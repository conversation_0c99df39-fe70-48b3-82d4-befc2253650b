import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';
import { updateFlowDiagramConstraints } from '@/services/flowDiagramApi';
import { getSectorUuidFromIndustryId } from '@/services/activitiesApi';
import { fetchEnergies, fetchEmissions } from '@/services/connectionFormApi';
import { EnergyResponse, EmissionResponse } from '@/components/ConnectionForm/types';
import { useToastContext } from '@/contexts/ToastContext';
import { useParams } from 'react-router-dom';

interface ConstraintsData {
  objectiveFunction: {
    selectedObjective: 'total-cost' | 'energy' | 'emissions' | null;
  };
  costConstraints: {
    totalCost: { value: string; currency: string };
    capitalCost: { value: string; currency: string };
    omCost: { value: string; currency: string };
    energyCost: { value: string; currency: string };
  };
  energyConstraints: {
    totalEnergyInput: { value: string; unit: string };
    selectedEnergies: string[];
    energyBounds: { [key: string]: { upperBound: { value: string; unit: string }; lowerBound: { value: string; unit: string } } };
  };
  emissionConstraints: {
    totalEmissions: { value: string; unit: string };
    selectedEmissions: string[];
    emissionBounds: { [key: string]: { upperBound: { value: string; unit: string } } };
  };
}

interface AddConstraintsModalProps {
  open: boolean;
  onClose: () => void;
  onSave?: (constraints: ConstraintsData) => void;
  scenarioId: string;
  // New props for API call
  userUuid?: string;
  sectorUuid?: string;
  scenarioName?: string;
  flowType?: string;
  industryId?: string;
}

const defaultConstraints: ConstraintsData = {
  objectiveFunction: {
    selectedObjective: null,
  },
  costConstraints: {
    totalCost: { value: '', currency: 'USD' },
    capitalCost: { value: '', currency: 'USD' },
    omCost: { value: '', currency: 'USD' },
    energyCost: { value: '', currency: 'USD' },
  },
  energyConstraints: {
    totalEnergyInput: { value: '', unit: 'KJ' },
    selectedEnergies: [],
    energyBounds: {},
  },
  emissionConstraints: {
    totalEmissions: { value: '', unit: 'kg CO₂e' },
    selectedEmissions: [],
    emissionBounds: {},
  },
};

const currencyOptions = ['USD', 'EUR', 'INR', 'GBP', 'JPY'];
const energyUnitOptions = ['KJ', 'J', 'GW'];
const emissionUnitOptions = ['kg CO₂e', 't CO₂e', 'g CO₂e'];

// Helper function to merge saved constraints with default structure
const mergeWithDefaults = (saved: any, defaults: ConstraintsData): ConstraintsData => {
  return {
    objectiveFunction: {
      ...defaults.objectiveFunction,
      ...saved?.objectiveFunction,
    },
    costConstraints: {
      ...defaults.costConstraints,
      ...saved?.costConstraints,
    },
    energyConstraints: {
      ...defaults.energyConstraints,
      ...saved?.energyConstraints,
      selectedEnergies: saved?.energyConstraints?.selectedEnergies || [],
      energyBounds: saved?.energyConstraints?.energyBounds || saved?.energyConstraints?.energyLimits || {},
    },
    emissionConstraints: {
      ...defaults.emissionConstraints,
      ...saved?.emissionConstraints,
      selectedEmissions: saved?.emissionConstraints?.selectedEmissions || saved?.emissionConstraints?.selectedEnergies || [],
      emissionBounds: saved?.emissionConstraints?.emissionBounds || saved?.emissionConstraints?.energyEmissionLimits || {},
    },
  };
};

export const AddConstraintsModal: React.FC<AddConstraintsModalProps> = ({
  open,
  onClose,
  onSave,
  scenarioId,
  userUuid,
  sectorUuid,
  scenarioName,
  flowType,
  industryId
}) => {
  const [constraints, setConstraints] = useState<ConstraintsData>(defaultConstraints);
  const [isSaving, setIsSaving] = useState(false);
  const [energies, setEnergies] = useState<EnergyResponse[]>([]);
  const [emissions, setEmissions] = useState<EmissionResponse[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const { toast } = useToastContext();
  const { industryId: paramIndustryId } = useParams<{ industryId: string }>();

  // Fetch energies and emissions data on mount
  useEffect(() => {
    const fetchData = async () => {
      setIsLoadingData(true);
      try {
        const [energiesData, emissionsData] = await Promise.all([
          fetchEnergies({ toast }),
          fetchEmissions({ toast })
        ]);
        setEnergies(energiesData);
        setEmissions(emissionsData);
      } catch (error) {
        console.error('Error fetching energies and emissions:', error);
      } finally {
        setIsLoadingData(false);
      }
    };

    if (open) {
      fetchData();
    }
  }, [open, toast]);

  // Load constraints from localStorage on mount
  useEffect(() => {
    const storageKey = `constraints_${scenarioId}`;
    const saved = localStorage.getItem(storageKey);
    if (saved) {
      try {
        const parsedConstraints = JSON.parse(saved);
        setConstraints(mergeWithDefaults(parsedConstraints, defaultConstraints));
      } catch (error) {
        console.error('Error loading saved constraints:', error);
        setConstraints(defaultConstraints);
      }
    }
  }, [scenarioId]);

  // Save constraints to localStorage whenever they change
  useEffect(() => {
    const storageKey = `constraints_${scenarioId}`;
    localStorage.setItem(storageKey, JSON.stringify(constraints));
  }, [constraints, scenarioId]);

  const handleObjectiveRadioChange = (value: 'total-cost' | 'energy' | 'emissions') => {
    setConstraints(prev => ({
      ...prev,
      objectiveFunction: {
        ...prev.objectiveFunction,
        selectedObjective: value
      }
    }));
  };

  const handleCostConstraintChange = (field: keyof ConstraintsData['costConstraints'], type: 'value' | 'currency', value: string) => {
    if (type === 'value') {
      // Only allow positive numbers for value
      if (value === '' || (/^\d*\.?\d*$/.test(value) && parseFloat(value) >= 0)) {
        setConstraints(prev => ({
          ...prev,
          costConstraints: {
            ...prev.costConstraints,
            [field]: {
              ...prev.costConstraints[field],
              value
            }
          }
        }));
      }
    } else {
      setConstraints(prev => ({
        ...prev,
        costConstraints: {
          ...prev.costConstraints,
          [field]: {
            ...prev.costConstraints[field],
            currency: value
          }
        }
      }));
    }
  };

  const handleEnergyTotalChange = (type: 'value' | 'unit', value: string) => {
    if (type === 'value') {
      // Only allow positive numbers for value
      if (value === '' || (/^\d*\.?\d*$/.test(value) && parseFloat(value) >= 0)) {
        setConstraints(prev => ({
          ...prev,
          energyConstraints: {
            ...prev.energyConstraints,
            totalEnergyInput: {
              ...prev.energyConstraints.totalEnergyInput,
              value
            }
          }
        }));
      }
    } else {
      setConstraints(prev => ({
        ...prev,
        energyConstraints: {
          ...prev.energyConstraints,
          totalEnergyInput: {
            ...prev.energyConstraints.totalEnergyInput,
            unit: value
          }
        }
      }));
    }
  };

  const handleEnergySelection = (energyType: string) => {
    if (!energyType || energyType === 'select-energy') return;

    setConstraints(prev => {
      const isAlreadySelected = prev.energyConstraints.selectedEnergies.includes(energyType);
      if (isAlreadySelected) return prev;

      const newSelectedEnergies = [...prev.energyConstraints.selectedEnergies, energyType];
      const newEnergyBounds = {
        ...prev.energyConstraints.energyBounds,
        [energyType]: {
          upperBound: { value: '', unit: 'KJ' },
          lowerBound: { value: '', unit: 'KJ' }
        }
      };

      return {
        ...prev,
        energyConstraints: {
          ...prev.energyConstraints,
          selectedEnergies: newSelectedEnergies,
          energyBounds: newEnergyBounds
        }
      };
    });
  };

  const handleRemoveEnergy = (energyType: string) => {
    setConstraints(prev => {
      const newSelectedEnergies = prev.energyConstraints.selectedEnergies.filter(e => e !== energyType);
      const newEnergyBounds = { ...prev.energyConstraints.energyBounds };
      delete newEnergyBounds[energyType];

      return {
        ...prev,
        energyConstraints: {
          ...prev.energyConstraints,
          selectedEnergies: newSelectedEnergies,
          energyBounds: newEnergyBounds
        }
      };
    });
  };

  const handleEnergyBoundChange = (energyType: string, boundType: 'upperBound' | 'lowerBound', type: 'value' | 'unit', value: string) => {
    if (type === 'value') {
      // Only allow positive numbers for value
      if (value === '' || (/^\d*\.?\d*$/.test(value) && parseFloat(value) >= 0)) {
        setConstraints(prev => ({
          ...prev,
          energyConstraints: {
            ...prev.energyConstraints,
            energyBounds: {
              ...prev.energyConstraints.energyBounds,
              [energyType]: {
                ...prev.energyConstraints.energyBounds[energyType],
                [boundType]: {
                  ...prev.energyConstraints.energyBounds[energyType][boundType],
                  value
                }
              }
            }
          }
        }));
      }
    } else {
      setConstraints(prev => ({
        ...prev,
        energyConstraints: {
          ...prev.energyConstraints,
          energyBounds: {
            ...prev.energyConstraints.energyBounds,
            [energyType]: {
              ...prev.energyConstraints.energyBounds[energyType],
              [boundType]: {
                ...prev.energyConstraints.energyBounds[energyType][boundType],
                unit: value
              }
            }
          }
        }
      }));
    }
  };

  const handleEmissionTotalChange = (type: 'value' | 'unit', value: string) => {
    if (type === 'value') {
      // Only allow positive numbers for value
      if (value === '' || (/^\d*\.?\d*$/.test(value) && parseFloat(value) >= 0)) {
        setConstraints(prev => ({
          ...prev,
          emissionConstraints: {
            ...prev.emissionConstraints,
            totalEmissions: {
              ...prev.emissionConstraints.totalEmissions,
              value
            }
          }
        }));
      }
    } else {
      setConstraints(prev => ({
        ...prev,
        emissionConstraints: {
          ...prev.emissionConstraints,
          totalEmissions: {
            ...prev.emissionConstraints.totalEmissions,
            unit: value
          }
        }
      }));
    }
  };

  const handleEmissionSelection = (emissionType: string) => {
    if (!emissionType || emissionType === 'select-emission') return;

    setConstraints(prev => {
      const isAlreadySelected = prev.emissionConstraints.selectedEmissions.includes(emissionType);
      if (isAlreadySelected) return prev;

      const newSelectedEmissions = [...prev.emissionConstraints.selectedEmissions, emissionType];
      const newEmissionBounds = {
        ...prev.emissionConstraints.emissionBounds,
        [emissionType]: {
          upperBound: { value: '', unit: 'kg CO₂e' }
        }
      };

      return {
        ...prev,
        emissionConstraints: {
          ...prev.emissionConstraints,
          selectedEmissions: newSelectedEmissions,
          emissionBounds: newEmissionBounds
        }
      };
    });
  };

  const handleRemoveEmission = (emissionType: string) => {
    setConstraints(prev => {
      const newSelectedEmissions = prev.emissionConstraints.selectedEmissions.filter(e => e !== emissionType);
      const newEmissionBounds = { ...prev.emissionConstraints.emissionBounds };
      delete newEmissionBounds[emissionType];

      return {
        ...prev,
        emissionConstraints: {
          ...prev.emissionConstraints,
          selectedEmissions: newSelectedEmissions,
          emissionBounds: newEmissionBounds
        }
      };
    });
  };

  const handleEmissionBoundChange = (emissionType: string, type: 'value' | 'unit', value: string) => {
    if (type === 'value') {
      // Only allow positive numbers for value
      if (value === '' || (/^\d*\.?\d*$/.test(value) && parseFloat(value) >= 0)) {
        setConstraints(prev => ({
          ...prev,
          emissionConstraints: {
            ...prev.emissionConstraints,
            emissionBounds: {
              ...prev.emissionConstraints.emissionBounds,
              [emissionType]: {
                ...prev.emissionConstraints.emissionBounds[emissionType],
                upperBound: {
                  ...prev.emissionConstraints.emissionBounds[emissionType].upperBound,
                  value
                }
              }
            }
          }
        }));
      }
    } else {
      setConstraints(prev => ({
        ...prev,
        emissionConstraints: {
          ...prev.emissionConstraints,
          emissionBounds: {
            ...prev.emissionConstraints.emissionBounds,
            [emissionType]: {
              ...prev.emissionConstraints.emissionBounds[emissionType],
              upperBound: {
                ...prev.emissionConstraints.emissionBounds[emissionType].upperBound,
                unit: value
              }
            }
          }
        }
      }));
    }
  };

  // Helper function to get current user UUID
  const getCurrentUserUuid = (): string => {
    const userData = localStorage.getItem('userData');
    if (!userData) {
      throw new Error('No user data found. Please log in again.');
    }

    try {
      const parsedUserData = JSON.parse(userData);
      if (!parsedUserData.uuid) {
        throw new Error('User UUID not found in user data.');
      }
      return parsedUserData.uuid;
    } catch (error) {
      throw new Error('Invalid user data format.');
    }
  };

  // Function to transform constraints data to API format
  const transformConstraintsToApiFormat = (constraints: ConstraintsData) => {
    // Ensure we have a valid objective function value
    const selectedObjective = constraints.objectiveFunction.selectedObjective;

    // Map frontend values to API values
    const objectiveMapping = {
      'total-cost': 'minimise_total_cost',
      'energy': 'minimise_energy_use',
      'emissions': 'minimise_emissions'
    };

    // Validate that the objective is one of the allowed values (if not null)
    const validObjectives = ['total-cost', 'energy', 'emissions'];
    if (selectedObjective && !validObjectives.includes(selectedObjective)) {
      throw new Error(`Invalid objective function: ${selectedObjective}. Must be one of: ${validObjectives.join(', ')}`);
    }

    // Transform the objective function value
    const apiObjectiveFunction = selectedObjective ? objectiveMapping[selectedObjective] : null;

    // Debug the transformation
    console.log('Frontend objective:', selectedObjective);
    console.log('API objective:', apiObjectiveFunction);

    // Create the structure consistent with frontend grouping and your specification
    const apiPayload = {
      objective_function: apiObjectiveFunction,
      cost_constraints: {
        total_cost_upper_limit: {
          value: constraints.costConstraints.totalCost.value ? parseFloat(constraints.costConstraints.totalCost.value) : null,
          currency: constraints.costConstraints.totalCost.currency,
        },
        capital_cost_upper_limit: {
          value: constraints.costConstraints.capitalCost.value ? parseFloat(constraints.costConstraints.capitalCost.value) : null,
          currency: constraints.costConstraints.capitalCost.currency,
        },
        om_cost_upper_limit: {
          value: constraints.costConstraints.omCost.value ? parseFloat(constraints.costConstraints.omCost.value) : null,
          currency: constraints.costConstraints.omCost.currency,
        },
        energy_cost_upper_limit: {
          value: constraints.costConstraints.energyCost.value ? parseFloat(constraints.costConstraints.energyCost.value) : null,
          currency: constraints.costConstraints.energyCost.currency,
        },
      },
      energy_constraints: {
        total_energy_input_upper_limit: {
          value: constraints.energyConstraints.totalEnergyInput.value ? parseFloat(constraints.energyConstraints.totalEnergyInput.value) : null,
          unit: constraints.energyConstraints.totalEnergyInput.unit,
        },
        energy: Object.fromEntries(
          Object.entries(constraints.energyConstraints.energyBounds).map(([key, value]) => [
            key,
            {
              upper_limit: {
                value: value.upperBound.value ? parseFloat(value.upperBound.value) : null,
                unit: value.upperBound.unit,
              },
              lower_limit: {
                value: value.lowerBound.value ? parseFloat(value.lowerBound.value) : null,
                unit: value.lowerBound.unit,
              },
            },
          ])
        ),
      },
      emission_constraints: {
        total_emissions_upper_limit: {
          value: constraints.emissionConstraints.totalEmissions.value ? parseFloat(constraints.emissionConstraints.totalEmissions.value) : null,
          unit: constraints.emissionConstraints.totalEmissions.unit,
        },
        emission: Object.fromEntries(
          Object.entries(constraints.emissionConstraints.emissionBounds).map(([key, value]) => [
            key,
            {
              upper_limit: {
                value: value.upperBound.value ? parseFloat(value.upperBound.value) : null,
                unit: value.upperBound.unit,
              },
            },
          ])
        ),
      },
    };

    return apiPayload;
  };

  const handleSave = async () => {
    if (isSaving) return;

    setIsSaving(true);

    try {
      // Get required parameters
      const currentUserUuid = userUuid || getCurrentUserUuid();
      const currentIndustryId = industryId || paramIndustryId;
      const currentSectorUuid = sectorUuid || (currentIndustryId ? await getSectorUuidFromIndustryId(currentIndustryId) : null);
      const currentScenarioName = scenarioName || scenarioId;
      const currentFlowType = flowType || 'SCENARIO_MAIN';

      if (!currentUserUuid || !currentSectorUuid || !currentScenarioName) {
        throw new Error('Missing required parameters for API call');
      }

      // Transform constraints to API format
      const apiConstraints = transformConstraintsToApiFormat(constraints);

      // Call the API
      await updateFlowDiagramConstraints(
        currentUserUuid,
        currentSectorUuid,
        currentScenarioName,
        currentFlowType,
        apiConstraints,
        { toast }
      );

      // Call the onSave callback if provided
      if (onSave) {
        onSave(constraints);
      }

      onClose();
    } catch (error) {
      toast({
        title: "Error Saving Constraints",
        description: error instanceof Error ? error.message : "Failed to save constraints",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Safe access to selectedEnergies arrays with fallbacks
  const availableEnergyOptions = energies
    .filter(energy => !(constraints.energyConstraints?.selectedEnergies || []).includes(energy.name))
    .map(energy => energy.name);

  const availableEmissionOptions = emissions
    .filter(emission => !(constraints.emissionConstraints?.selectedEmissions || []).includes(emission.name))
    .map(emission => emission.name);



  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-3">
          <DialogTitle className="text-2xl font-bold">Add Constraints</DialogTitle>
          <DialogDescription className="text-base">
            Define optimization objectives and system constraints for your scenario.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-8 py-6">
          {/* Objective Function */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Objective Function</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <RadioGroup
                value={constraints.objectiveFunction.selectedObjective || ''}
                onValueChange={handleObjectiveRadioChange}
                className="space-y-4"
              >
                <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-gray-50">
                  <RadioGroupItem value="total-cost" id="total-cost" />
                  <Label htmlFor="total-cost" className="text-base cursor-pointer">Minimise Total Cost</Label>
                </div>
                <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-gray-50">
                  <RadioGroupItem value="energy" id="energy" />
                  <Label htmlFor="energy" className="text-base cursor-pointer">Minimise Energy Use</Label>
                </div>
                <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-gray-50">
                  <RadioGroupItem value="emissions" id="emissions" />
                  <Label htmlFor="emissions" className="text-base cursor-pointer">Minimise Emissions</Label>
                </div>
              </RadioGroup>
            </CardContent>
          </Card>

          {/* Cost or Budget Constraints */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Cost or Budget Constraints</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="totalCost" className="text-base font-medium">Total Cost</Label>
                  <div className="flex gap-2">
                    <Input
                      id="totalCost"
                      type="text"
                      placeholder="Upper Bound"
                      value={constraints.costConstraints.totalCost.value}
                      onChange={(e) => handleCostConstraintChange('totalCost', 'value', e.target.value)}
                      className="h-11 flex-1"
                    />
                    <Select value={constraints.costConstraints.totalCost.currency} onValueChange={(value) => handleCostConstraintChange('totalCost', 'currency', value)}>
                      <SelectTrigger className="h-11 w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {currencyOptions.map((currency) => (
                          <SelectItem key={currency} value={currency}>{currency}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="capitalCost" className="text-base font-medium">Capital Cost</Label>
                  <div className="flex gap-2">
                    <Input
                      id="capitalCost"
                      type="text"
                      placeholder="Upper Bound"
                      value={constraints.costConstraints.capitalCost.value}
                      onChange={(e) => handleCostConstraintChange('capitalCost', 'value', e.target.value)}
                      className="h-11 flex-1"
                    />
                    <Select value={constraints.costConstraints.capitalCost.currency} onValueChange={(value) => handleCostConstraintChange('capitalCost', 'currency', value)}>
                      <SelectTrigger className="h-11 w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {currencyOptions.map((currency) => (
                          <SelectItem key={currency} value={currency}>{currency}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="omCost" className="text-base font-medium">O&M Cost</Label>
                  <div className="flex gap-2">
                    <Input
                      id="omCost"
                      type="text"
                      placeholder="Upper Bound"
                      value={constraints.costConstraints.omCost.value}
                      onChange={(e) => handleCostConstraintChange('omCost', 'value', e.target.value)}
                      className="h-11 flex-1"
                    />
                    <Select value={constraints.costConstraints.omCost.currency} onValueChange={(value) => handleCostConstraintChange('omCost', 'currency', value)}>
                      <SelectTrigger className="h-11 w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {currencyOptions.map((currency) => (
                          <SelectItem key={currency} value={currency}>{currency}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="energyCost" className="text-base font-medium">Energy Cost</Label>
                  <div className="flex gap-2">
                    <Input
                      id="energyCost"
                      type="text"
                      placeholder="Upper Bound"
                      value={constraints.costConstraints.energyCost.value}
                      onChange={(e) => handleCostConstraintChange('energyCost', 'value', e.target.value)}
                      className="h-11 flex-1"
                    />
                    <Select value={constraints.costConstraints.energyCost.currency} onValueChange={(value) => handleCostConstraintChange('energyCost', 'currency', value)}>
                      <SelectTrigger className="h-11 w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {currencyOptions.map((currency) => (
                          <SelectItem key={currency} value={currency}>{currency}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Energy Constraints */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Energy Constraints</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="totalEnergyInput" className="text-base font-medium">Total Energy Input</Label>
                <div className="flex gap-2">
                  <Input
                    id="totalEnergyInput"
                    type="text"
                    placeholder="Upper Bound"
                    value={constraints.energyConstraints.totalEnergyInput.value}
                    onChange={(e) => handleEnergyTotalChange('value', e.target.value)}
                    className="h-11 flex-1"
                  />
                  <Select value={constraints.energyConstraints.totalEnergyInput.unit} onValueChange={(value) => handleEnergyTotalChange('unit', value)}>
                    <SelectTrigger className="h-11 w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {energyUnitOptions.map((unit) => (
                        <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-base font-medium">Energy Types</Label>
                  <Select value="" onValueChange={handleEnergySelection} disabled={isLoadingData}>
                    <SelectTrigger className="h-11">
                      <SelectValue placeholder={isLoadingData ? "Loading energies..." : "Select an energy type to add"} />
                    </SelectTrigger>
                    <SelectContent>
                      {availableEnergyOptions.map((energyType) => (
                        <SelectItem key={energyType} value={energyType}>
                          {energyType}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {(constraints.energyConstraints?.selectedEnergies || []).length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-3">
                      {(constraints.energyConstraints?.selectedEnergies || []).map((energyType) => (
                        <Badge key={energyType} variant="secondary" className="px-3 py-1 text-sm">
                          {energyType}
                          <button
                            onClick={() => handleRemoveEnergy(energyType)}
                            className="ml-2 hover:text-red-600"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                {/* Energy Bounds for Selected Types */}
                {(constraints.energyConstraints?.selectedEnergies || []).length > 0 && (
                  <div className="space-y-4">
                    <Label className="text-base font-medium">Energy Bounds</Label>
                    <div className="space-y-4">
                      {(constraints.energyConstraints?.selectedEnergies || []).map((energyType) => (
                        <div key={energyType} className="p-4 border rounded-lg space-y-3">
                          <Label className="font-semibold text-base">{energyType}</Label>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor={`${energyType}-upper`} className="text-sm text-gray-600">Upper Bound</Label>
                              <div className="flex gap-2">
                                <Input
                                  id={`${energyType}-upper`}
                                  type="text"
                                  placeholder="Upper Bound"
                                  value={constraints.energyConstraints.energyBounds[energyType]?.upperBound.value || ''}
                                  onChange={(e) => handleEnergyBoundChange(energyType, 'upperBound', 'value', e.target.value)}
                                  className="h-10 flex-1"
                                />
                                <Select
                                  value={constraints.energyConstraints.energyBounds[energyType]?.upperBound.unit || 'KJ'}
                                  onValueChange={(value) => handleEnergyBoundChange(energyType, 'upperBound', 'unit', value)}
                                >
                                  <SelectTrigger className="h-10 w-20">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {energyUnitOptions.map((unit) => (
                                      <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor={`${energyType}-lower`} className="text-sm text-gray-600">Lower Bound</Label>
                              <div className="flex gap-2">
                                <Input
                                  id={`${energyType}-lower`}
                                  type="text"
                                  placeholder="Lower Bound"
                                  value={constraints.energyConstraints.energyBounds[energyType]?.lowerBound.value || ''}
                                  onChange={(e) => handleEnergyBoundChange(energyType, 'lowerBound', 'value', e.target.value)}
                                  className="h-10 flex-1"
                                />
                                <Select
                                  value={constraints.energyConstraints.energyBounds[energyType]?.lowerBound.unit || 'KJ'}
                                  onValueChange={(value) => handleEnergyBoundChange(energyType, 'lowerBound', 'unit', value)}
                                >
                                  <SelectTrigger className="h-10 w-20">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {energyUnitOptions.map((unit) => (
                                      <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Emission Constraints */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Emission Constraints</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="totalEmissions" className="text-base font-medium">Total Emissions</Label>
                <div className="flex gap-2">
                  <Input
                    id="totalEmissions"
                    type="text"
                    placeholder="Upper Bound"
                    value={constraints.emissionConstraints.totalEmissions.value}
                    onChange={(e) => handleEmissionTotalChange('value', e.target.value)}
                    className="h-11 flex-1"
                  />
                  <Select value={constraints.emissionConstraints.totalEmissions.unit} onValueChange={(value) => handleEmissionTotalChange('unit', value)}>
                    <SelectTrigger className="h-11 w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {emissionUnitOptions.map((unit) => (
                        <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-base font-medium">Emission Types</Label>
                  <Select value="" onValueChange={handleEmissionSelection} disabled={isLoadingData}>
                    <SelectTrigger className="h-11">
                      <SelectValue placeholder={isLoadingData ? "Loading emissions..." : "Select an emission type to add"} />
                    </SelectTrigger>
                    <SelectContent>
                      {availableEmissionOptions.map((emissionType) => (
                        <SelectItem key={emissionType} value={emissionType}>
                          {emissionType}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {(constraints.emissionConstraints?.selectedEmissions || []).length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-3">
                      {(constraints.emissionConstraints?.selectedEmissions || []).map((emissionType) => (
                        <Badge key={emissionType} variant="secondary" className="px-3 py-1 text-sm">
                          {emissionType}
                          <button
                            onClick={() => handleRemoveEmission(emissionType)}
                            className="ml-2 hover:text-red-600"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                {/* Emission Bounds for Selected Types */}
                {(constraints.emissionConstraints?.selectedEmissions || []).length > 0 && (
                  <div className="space-y-4">
                    <Label className="text-base font-medium">Emission Bounds</Label>
                    <div className="space-y-4">
                      {(constraints.emissionConstraints?.selectedEmissions || []).map((emissionType) => (
                        <div key={emissionType} className="p-4 border rounded-lg space-y-3">
                          <Label className="font-semibold text-base">{emissionType}</Label>
                          <div className="space-y-2">
                            <Label htmlFor={`${emissionType}-emission-upper`} className="text-sm text-gray-600">Upper Bound</Label>
                            <div className="flex gap-2">
                              <Input
                                id={`${emissionType}-emission-upper`}
                                type="text"
                                placeholder="Upper Bound"
                                value={constraints.emissionConstraints.emissionBounds[emissionType]?.upperBound.value || ''}
                                onChange={(e) => handleEmissionBoundChange(emissionType, 'value', e.target.value)}
                                className="h-10 flex-1"
                              />
                              <Select
                                value={constraints.emissionConstraints.emissionBounds[emissionType]?.upperBound.unit || 'kg CO₂e'}
                                onValueChange={(value) => handleEmissionBoundChange(emissionType, 'unit', value)}
                              >
                                <SelectTrigger className="h-10 w-32">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {emissionUnitOptions.map((unit) => (
                                    <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="gap-3">
          <Button variant="outline" onClick={onClose} className="h-11 px-6" disabled={isSaving}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="h-11 px-6" disabled={isSaving}>
            {isSaving ? 'Saving...' : 'Save Constraints'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
