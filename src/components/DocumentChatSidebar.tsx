import React, { useState, useMemo } from 'react';
import { Plus, FileText, Edit, Trash2, Search, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { API_BASE_URL } from '@/utils/endPoints';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

export interface Document {
  uuid: string;
  filename: string;
  s3_url: string;
  status: string;
}

interface DocumentChatSidebarProps {
  documentList: Document[];
  selectedDocumentId: string | null;
  onSelect: (id: string) => void;
  onNewSession: () => void;
  onDocumentDeleted: (documentId: string) => void;
  isLoading?: boolean;
}

const DocumentChatSidebar: React.FC<DocumentChatSidebarProps> = ({
  documentList,
  selectedDocumentId,
  onSelect,
  onNewSession,
  onDocumentDeleted,
  isLoading = false
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [deletingDocuments, setDeletingDocuments] = useState<Set<string>>(new Set());
  const { toast } = useToast();
  
  const filteredDocuments = useMemo(() => {
    if (!searchQuery.trim()) return documentList;
    
    return documentList.filter(doc => 
      doc.filename.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [documentList, searchQuery]);

  const handleDelete = async (documentId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (deletingDocuments.has(documentId)) return;
    
    try {
      setDeletingDocuments(prev => new Set(prev).add(documentId));
      
      const accessToken = localStorage.getItem('accessToken');
      if (!accessToken) {
        toast({
          title: "Authentication Required",
          description: "Please log in to delete documents",
          variant: "destructive",
        });
        return;
      }
      
      const response = await fetch(`${API_BASE_URL}/documents/${documentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete document');
      }
      
      setTimeout(() => {
        onDocumentDeleted(documentId);
        setDeletingDocuments(prev => {
          const updated = new Set(prev);
          updated.delete(documentId);
          return updated;
        });
        
        toast({
          title: "Document deleted",
          description: "The document has been removed successfully",
        });
      }, 300);
      
    } catch (error) {
      console.error('Error deleting document:', error);
      setDeletingDocuments(prev => {
        const updated = new Set(prev);
        updated.delete(documentId);
        return updated;
      });
      
      toast({
        title: "Error",
        description: "Failed to delete document. Please try again later.",
        variant: "destructive",
      });
    }
  };
    
  return (
    <div className="w-72 border-r dark:border-border/60 flex flex-col">
      <div className="p-4 border-b dark:border-border/60">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-medium">Documents</h3>
          <Button 
            variant="outline" 
            size="sm"
            onClick={onNewSession}
            className="flex items-center gap-1"
          >
            <Plus size={14} />
            New
          </Button>
        </div>
        
        <Input
          placeholder="Search documents..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full"
        />
      </div>
      
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
            <div className="h-6 w-6 border-2 border-t-transparent rounded-full animate-spin mb-2"></div>
            <p className="text-sm">Loading documents...</p>
          </div>
        ) : filteredDocuments.length > 0 ? (
          filteredDocuments.map((doc) => {
            const isDeleting = deletingDocuments.has(doc.uuid);
            return (
              <div 
                key={doc.uuid}
                className={`p-3 cursor-pointer flex items-center justify-between hover:bg-accent/50 group transition-all duration-200
                  ${selectedDocumentId === doc.uuid ? 'bg-accent/70 dark:bg-background/40 border-l-2 border-primary' : ''}
                  ${isDeleting ? 'opacity-50 animate-fade-out' : ''}`}
              >
                <div 
                  className="flex items-center gap-2 flex-1"
                  onClick={() => !isDeleting && onSelect(doc.uuid)}
                >
                  <div className={`p-1 rounded ${selectedDocumentId === doc.uuid ? 'bg-primary/10' : ''}`}>
                    <FileText size={16} className={`${selectedDocumentId === doc.uuid ? 'text-primary' : 'text-muted-foreground'}`} />
                  </div>
                  <span className="truncate text-sm">{doc.filename}</span>
                </div>
                
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className={`opacity-0 group-hover:opacity-100 transition-opacity h-7 w-7 ${isDeleting ? 'pointer-events-none' : ''}`}
                  onClick={(e) => handleDelete(doc.uuid, e)}
                >
                  {isDeleting ? (
                    <div className="h-3 w-3 border-2 border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <Trash2 size={14} className="text-muted-foreground hover:text-destructive" />
                  )}
                </Button>
              </div>
            );
          })
        ) : (
          <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
            <div className="bg-muted/50 p-3 rounded-full mb-2">
              <FileText size={20} />
            </div>
            <p className="text-sm">No documents found</p>
            {searchQuery && (
              <Button 
                variant="link" 
                size="sm" 
                onClick={() => setSearchQuery('')}
                className="mt-1"
              >
                Clear search
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentChatSidebar;
