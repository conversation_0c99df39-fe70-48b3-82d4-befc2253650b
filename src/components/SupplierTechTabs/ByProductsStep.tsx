import { filterValidNodes } from "@/utils/dropdownUtils";
import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Plus, Trash } from "lucide-react";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  FormData,
  byproducts,
  units,
  emissions,
  EnergyByProduct,
  MaterialByProduct,
  EmissionByProduct,
  techs,
  TechnologyResponse,
  MaterialResponse,
  EnergyResponse,
  EmissionResponse
} from "./types";

interface ByProductsStepProps {
  formData: FormData;
  updateFormField: (section: string, field: string, value: string) => void;
  updateField: (field: string, value: string) => void;
  errors: Record<string, string>;
  availableNodes?: any[];
  technologyAutoFillLabel?: string;
  availableTechnologies?: string[];
  technologies: string[];
  activeTechnology: string;
  setActiveTechnology: (tech: string) => void;
  onAddTechnology?: () => void;
  readOnly?: boolean;
  // API data props
  apiTechnologies?: TechnologyResponse[];
  apiMaterials?: MaterialResponse[];
  apiEnergies?: EnergyResponse[];
  apiEmissions?: EmissionResponse[];
  isLoadingApiData?: boolean;
}

export const ByProductsStep: React.FC<ByProductsStepProps> = ({
  formData,
  updateFormField,
  updateField,
  errors,
  availableNodes = [],
  technologyAutoFillLabel,
  availableTechnologies = [],
  technologies,
  activeTechnology,
  setActiveTechnology,
  onAddTechnology,
  readOnly = true,
  apiTechnologies = [],
  apiMaterials = [],
  apiEnergies = [],
  apiEmissions = [],
  isLoadingApiData = false
}) => {
  // Get data from API or fallback to hardcoded arrays
  const availableTechnologiesData = apiTechnologies.length > 0
    ? apiTechnologies.map(tech => tech.name).filter(name => name && name.trim() !== "")
    : techs.filter(tech => tech && tech.trim() !== "");

  const availableMaterialsData = apiMaterials.length > 0
    ? apiMaterials.map(material => material.name).filter(name => name && name.trim() !== "")
    : byproducts.filter(material => material && material.trim() !== "");

  const availableEnergiesData = apiEnergies.length > 0
    ? apiEnergies.map(energy => energy.name).filter(name => name && name.trim() !== "")
    : byproducts.filter(energy => energy && energy.trim() !== "");

  const availableEmissionsData = apiEmissions.length > 0
    ? apiEmissions.map(emission => emission.name).filter(name => name && name.trim() !== "")
    : emissions.filter(emission => emission && emission.trim() !== "");

  // Initialize the state with the current technology or default to first available
  const [selectedTechnology, setSelectedTechnology] = useState<string>(
    formData.byproductTechnology || availableTechnologies[0] || availableTechnologiesData[0] || ""
  );

  const [energyByProducts, setEnergyByProducts] = useState<EnergyByProduct[]>(formData?.byproductEnergy || [
    {
      byproduct: formData.byproductEnergy?.[0]?.byproduct || "",
      unit: formData.byproductEnergy?.[0]?.unit || "GJ",
      bppo: formData.byproductEnergy?.[0]?.bppo || "",
      connect: formData.byproductEnergy?.[0]?.connect || "",
      replaced: formData.byproductEnergy?.[0]?.replaced || "",
      emissions: [], // Initialize with empty emissions array
    }
  ]);

  const [materialByProducts, setMaterialByProducts] = useState<MaterialByProduct[]>(formData?.byproductMat || [
    {
      byproduct: formData.byproductMat?.[0]?.byproduct || "",
      unit: formData.byproductMat?.[0]?.unit || "Tonnes",
      bppo: formData.byproductMat?.[0]?.bppo || "",
      connect: formData.byproductMat?.[0]?.connect || "",
      replaced: formData.byproductMat?.[0]?.replaced || "",
      techEmissionFactor: formData.byproductMat?.[0]?.techEmissionFactor || "",
      emissionFactor: formData.byproductMat?.[0]?.emissionFactor || "",
      emissionUnit: formData.byproductMat?.[0]?.emissionUnit || "",
    }
  ]);

  // Filter out invalid nodes including "input1"
  const validNodes = filterValidNodes(availableNodes);

  // Effect to update the form data with the byproduct technology when it changes
  useEffect(() => {
    // Set the default technology if none is selected yet
    if (selectedTechnology) {
      updateField('byproductTechnology', selectedTechnology);
    } else if (availableTechnologies.length > 0) {
      const defaultTech = availableTechnologies[0];
      setSelectedTechnology(defaultTech);
      updateField('byproductTechnology', defaultTech);
    }

    // Track valid connections for debugging
    const validEnergyConnections = energyByProducts
      .filter(energy => energy.byproduct && energy.connect)
      .map(energy => ({
        type: 'energy-byproduct',
        byproduct: energy.byproduct,
        destination: energy.connect === "null" ? null : energy.connect
      }));
      
    const validMaterialConnections = materialByProducts
      .filter(material => material.byproduct && material.connect)
      .map(material => ({
        type: 'material-byproduct',
        byproduct: material.byproduct,
        destination: material.connect === "null" ? null : material.connect
      }));
      
    if (validEnergyConnections.length > 0 || validMaterialConnections.length > 0) {
      // console.log("Valid byproduct connections:", [
      //   ...validEnergyConnections,
      //   ...validMaterialConnections
      // ]);
    }
  }, [selectedTechnology, availableTechnologies, energyByProducts, materialByProducts]);

  // Handle technology change for the entire by-products section
  const handleTechnologyChange = (value: string) => {
    setSelectedTechnology(value);
    updateField('byproductTechnology', value);
  };

  const addEnergyByProduct = () => {
    const newEnergyByProduct: EnergyByProduct = {
      byproduct: "",
      unit: "GJ",
      bppo: "",
      connect: "",
      replaced: "",
      emissions: [], // Initialize with empty emissions array
    };
    setEnergyByProducts([...energyByProducts, newEnergyByProduct]);
  };

  const addMaterialByProduct = () => {
    const newMaterialByProduct: MaterialByProduct = {
      byproduct: "",
      unit: "Tonnes",
      bppo: "",
      connect: "",
      replaced: "",
      techEmissionFactor: "",
      emissionFactor: "",
      emissionUnit: "",
    };
    setMaterialByProducts([...materialByProducts, newMaterialByProduct]);
  };

  // Fix the type error by using a generic type parameter and type guard
  const updateEnergyByProduct = <K extends keyof EnergyByProduct>(
    index: number,
    field: K,
    value: EnergyByProduct[K]
  ) => {
    const updatedEnergyByProducts = [...energyByProducts];
    // Handle special case for emissions array to avoid type issues
    if (field === 'emissions' as K) {
      updatedEnergyByProducts[index][field] = value;
    } else {
      // For string fields, assign directly
      updatedEnergyByProducts[index][field] = value;
    }
    setEnergyByProducts(updatedEnergyByProducts);
    // Update formData to reflect changes
    if (index === 0) {
      updateFormField('byproductEnergy', field as string, value as string);
    }
  };

  const updateMaterialByProduct = <K extends keyof MaterialByProduct>(
    index: number,
    field: K,
    value: MaterialByProduct[K]
  ) => {
    const updatedMaterialByProducts = [...materialByProducts];
    updatedMaterialByProducts[index][field] = value;
    setMaterialByProducts(updatedMaterialByProducts);
    // Update formData to reflect changes
    if (index === 0) {
      updateFormField('byproductMat', field as string, value as string);
    }
  };

  // const removeEnergyByProduct = (index: number) => {
  //   const updatedEnergyByProducts = [...energyByProducts];
  //   updatedEnergyByProducts.splice(index, 1);
  //   setEnergyByProducts(updatedEnergyByProducts);
  // };

  // const removeMaterialByProduct = (index: number) => {
  //   const updatedMaterialByProducts = [...materialByProducts];
  //   updatedMaterialByProducts.splice(index, 1);
  //   setMaterialByProducts(updatedMaterialByProducts);
  // };

  // New methods for emissions handling within energy by-products
  const addEmissionToEnergyByProduct = (energyByProductIndex: number) => {
    const updatedEnergyByProducts = [...energyByProducts];
    const newEmission: EmissionByProduct = {
      id: `emission-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      name: "",
      factor: "",
      unit: "kg"
    };
    
    // Ensure the emissions array exists
    if (!updatedEnergyByProducts[energyByProductIndex].emissions) {
      updatedEnergyByProducts[energyByProductIndex].emissions = [];
    }
    
    // Add the new emission to the array
    updatedEnergyByProducts[energyByProductIndex].emissions!.push(newEmission);
    setEnergyByProducts(updatedEnergyByProducts);
  };

  const updateEmissionInEnergyByProduct = (
    energyByProductIndex: number,
    emissionIndex: number,
    field: keyof EmissionByProduct,
    value: string
  ) => {
    const updatedEnergyByProducts = [...energyByProducts];
    
    if (updatedEnergyByProducts[energyByProductIndex].emissions) {
      updatedEnergyByProducts[energyByProductIndex].emissions![emissionIndex][field] = value;
      setEnergyByProducts(updatedEnergyByProducts);
    }
  };

  const removeEmissionFromEnergyByProduct = (energyByProductIndex: number, emissionIndex: number) => {
    const updatedEnergyByProducts = [...energyByProducts];
    
    if (updatedEnergyByProducts[energyByProductIndex].emissions) {
      updatedEnergyByProducts[energyByProductIndex].emissions!.splice(emissionIndex, 1);
      setEnergyByProducts(updatedEnergyByProducts);
    }
  };

  // Save complete by-product data to formData
  useEffect(() => {
    updateField('energyByProducts', JSON.stringify(energyByProducts));
    updateField('materialByProducts', JSON.stringify(materialByProducts));
  }, [energyByProducts, materialByProducts]);

  return (
    <>
      <Tabs value={activeTechnology} onValueChange={readOnly ? undefined : setActiveTechnology} className="w-full">
        {/* <TabsList className="mb-2 w-full overflow-x-auto flex whitespace-nowrap">
          {technologies.map((tech) => (
            <TabsTrigger
              key={tech}
              value={tech}
              className={`min-w-[100px] flex-shrink-0 ${readOnly ? 'cursor-not-allowed opacity-70' : ''}`}
              disabled={readOnly}
            >
              {tech}
            </TabsTrigger>
          ))}
        </TabsList> */}

        {technologies.map((tech) => (
          <TabsContent key={tech} value={tech}>
            {renderTechnologyContent(tech)}
          </TabsContent>
        ))}
      </Tabs>
    </>
  );

  function renderTechnologyContent(technology: string) {
    return (
      <>

      {/* Energy By-product Section */}
      <div className="border-2 border-green-300 rounded-md mb-3 p-3">
        <div className="flex items-center justify-between mb-1">
          <div className="font-semibold">Energy By-product</div>
          {!readOnly && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addEnergyByProduct}
              className="h-6 px-2 text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Energy By-product
            </Button>
          )}
        </div>
        {energyByProducts.map((energyByProduct, index) => (
          <div key={index} className="mb-4 p-3 border rounded-md">
            <div className="flex items-center justify-between mb-2">
              <div className="font-semibold text-sm">By-product {index + 1}</div>
              {/* <Button 
                type="button" 
                variant="outline" 
                size="sm" 
                onClick={() => removeEnergyByProduct(index)}
                className="h-6 px-2 text-xs"
              >
                <Trash className="h-3 w-3 mr-1" />
                Remove
              </Button> */}
            </div>
            
            <div className="flex flex-col md:flex-row gap-2">
              <div className="flex-1 min-w-0">
                <label>Name</label>
                {readOnly ? (
                  <div className="w-full border rounded px-2 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {energyByProduct.byproduct || (isLoadingApiData ? "Loading energies..." : "Select")}
                  </div>
                ) : (
                  <select
                    className="w-full border rounded px-2 py-2"
                    value={energyByProduct.byproduct}
                    onChange={(e) => updateEnergyByProduct(index, 'byproduct', e.target.value as string)}
                    disabled={isLoadingApiData}
                  >
                    <option value="">{isLoadingApiData ? "Loading energies..." : "Select"}</option>
                    {availableEnergiesData.map(v => <option key={v} value={v}>{v}</option>)}
                  </select>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <label>Unit</label>
                {readOnly ? (
                  <div className="w-full border rounded px-2 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {energyByProduct.unit}
                  </div>
                ) : (
                  <select
                    className="w-full border rounded px-2 py-2"
                    value={energyByProduct.unit}
                    onChange={(e) => updateEnergyByProduct(index, 'unit', e.target.value as string)}
                  >
                    {units.map(v => <option key={v} value={v}>{v}</option>)}
                  </select>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <label>BP/PO</label>
                <Input
                  type="text"
                  value={energyByProduct.bppo}
                  onChange={(e) => updateEnergyByProduct(index, 'bppo', e.target.value as string)}
                  disabled={readOnly}
                />
              </div>
            </div>
            
            {/* <div className="flex flex-col md:flex-row gap-2 mt-2">
              <div className="w-full md:w-1/2">
                <label>Energy replaced</label>
                <Input 
                  type="text"
                  value={energyByProduct.replaced}
                  onChange={(e) => updateEnergyByProduct(index, 'replaced', e.target.value as string)}
                />
              </div>
            </div> */}


          </div>
        ))}
      </div>

      {/* Material By-product Section */}
      <div className="border-2 border-purple-300 rounded-md mb-3 p-3">
        <div className="flex items-center justify-between mb-1">
          <div className="font-semibold">Material By-product</div>
          {!readOnly && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addMaterialByProduct}
              className="h-6 px-2 text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Material By-product
            </Button>
          )}
        </div>
        {materialByProducts.map((materialByProduct, index) => (
          <div key={index} className="mb-4 p-3 border rounded-md">
            <div className="flex items-center justify-between mb-2">
              <div className="font-semibold text-sm">By-product {index + 1}</div>
              {/* <Button 
                type="button" 
                variant="outline" 
                size="sm" 
                onClick={() => removeMaterialByProduct(index)}
                className="h-6 px-2 text-xs"
              >
                <Trash className="h-3 w-3 mr-1" />
                Remove
              </Button> */}
            </div>
            
            <div className="flex flex-col md:flex-row gap-2">
              <div className="flex-1 min-w-0">
                <label>Name</label>
                {readOnly ? (
                  <div className="w-full border rounded px-2 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {materialByProduct.byproduct || (isLoadingApiData ? "Loading materials..." : "Select")}
                  </div>
                ) : (
                  <select
                    className="w-full border rounded px-2 py-2"
                    value={materialByProduct.byproduct}
                    onChange={(e) => updateMaterialByProduct(index, 'byproduct', e.target.value as string)}
                    disabled={isLoadingApiData}
                  >
                    <option value="">{isLoadingApiData ? "Loading materials..." : "Select"}</option>
                    {availableMaterialsData.map(v => <option key={v} value={v}>{v}</option>)}
                  </select>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <label>Unit</label>
                {readOnly ? (
                  <div className="w-full border rounded px-2 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {materialByProduct.unit}
                  </div>
                ) : (
                  <select
                    className="w-full border rounded px-2 py-2"
                    value={materialByProduct.unit}
                    onChange={(e) => updateMaterialByProduct(index, 'unit', e.target.value as string)}
                  >
                    {units.map(v => <option key={v} value={v}>{v}</option>)}
                  </select>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <label>BP/PO</label>
                <Input
                  type="text"
                  value={materialByProduct.bppo}
                  onChange={(e) => updateMaterialByProduct(index, 'bppo', e.target.value as string)}
                  disabled={readOnly}
                />
              </div>
            </div>
            
            {/* <div className="flex flex-col md:flex-row gap-2 mt-2">
              <div className="w-full md:w-1/2">
                <label>Energy replaced</label>
                <Input 
                  type="text"
                  value={materialByProduct.replaced}
                  onChange={(e) => updateMaterialByProduct(index, 'replaced', e.target.value as string)}
                />
              </div>
            </div> */}
          </div>
        ))}
      </div>
      </>
    );
  }
};
