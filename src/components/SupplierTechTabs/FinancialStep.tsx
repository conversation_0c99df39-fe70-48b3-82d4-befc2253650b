
import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { FormData, OutputForm } from "./types";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronDown, ChevronUp } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Capacity units suitable for industrial processes
const capacityUnits = [
  "Tonnes/day",
  "Tonnes/hour",
  "Tonnes/year",
  "MW",
  "GW",
  "m³/day",
  "m³/hour",
  "Barrels/day",
  "Units/day",
  "Units/hour"
];

interface FinancialStepProps {
  formData: FormData;
  updateFormField: (section: string, field: string, value: string) => void;
  errors: Record<string, string>;
  outputs: OutputForm[];
  availableNodes?: Array<{ id: string; data: { label?: string } }>;
  technologies: string[];
  readOnly?: boolean;
}

type TechActivityPair = {
  technology: string;
  activity: string;
  activityName: string;
  source: string;
  id: string;
};

export const FinancialStep: React.FC<FinancialStepProps> = ({
  formData,
  updateFormField,
  errors,
  outputs,
  availableNodes = [],
  technologies,
  readOnly = false
}) => {
  // Track open/closed state for collapsible sections
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({});
  


  // Toggle open/closed state for a section
  const toggleSection = (id: string) => {
    setOpenSections(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // Helper function to get activity name from ID with better error handling
  const getActivityName = (activityId: string): string => {
    if (!activityId || activityId === "N/A") return "N/A";
    
    // Try to find the node in availableNodes
    const node = availableNodes.find(node => node.id === activityId);
    if (node?.data?.label) {
      return node.data.label;
    }
    
    // If we can't find it in availableNodes, try to extract from the activity ID
    // Some activity IDs might be in the format "node-X"
    if (activityId.startsWith('node-')) {
      return `Activity ${activityId.split('-')[1]}`;
    }
    
    // Last fallback
    return "Activity";
  };

  // Get the current activity name from source node or form data
  const getCurrentActivityName = (): string => {
    // If there's a specific activity in form data, use it
    if (formData.activity) {
      return formData.activity;
    }
    
    // Check if any available node is the source (current) node
    const currentNode = availableNodes.find(node => 
      node.id && outputs.length > 0 && !outputs[0].targetNode.includes(node.id)
    );
    
    if (currentNode?.data?.label) {
      return currentNode.data.label;
    }
    
    // Fallback to a default
    return "Current Activity";
  };

  // Create technology-activity pairs from the technologies array
  const extractTechActivityPairs = (): TechActivityPair[] => {
    const pairs: TechActivityPair[] = [];

    // Get the current activity name
    const currentActivityName = getCurrentActivityName();
    const currentActivity = "current"; // Use a consistent ID for current activity

    // Use the technologies array passed from parent
    technologies.forEach((tech, index) => {
      pairs.push({
        technology: tech,
        activity: currentActivity,
        activityName: currentActivityName,
        source: "technology",
        id: `tech-${index}`
      });
    });

    // If we found no pairs, create a default one
    if (pairs.length === 0) {
      pairs.push({
        technology: "Technology 1",
        activity: currentActivity,
        activityName: currentActivityName,
        source: "default",
        id: "default-tech"
      });
    }

    return pairs;
  };

  const techActivityPairs = extractTechActivityPairs();

  // Show default financial section if we have no tech-activity pairs
  const showDefaultFinancial = techActivityPairs.length === 0;

  // Create a unique financial data key for a tech-activity pair
  const getFinancialKey = (pair: TechActivityPair) => {
    return `${pair.source}-${pair.technology}-${pair.activity}`.replace(/\s+/g, '_');
  };

  // Get the financial data for a specific pair
  const getFinancialData = (pair: TechActivityPair) => {
    const key = getFinancialKey(pair);

    // If we have specific financial data for this pair in the form data
    if (formData.financialEntries && formData.financialEntries[key]) {
      return formData.financialEntries[key];
    }

    // For existing data, use the main financial data as the source of truth
    // and ensure all fields are properly mapped with backward compatibility
    const mainFinancial = formData.financial;
    if (mainFinancial && (mainFinancial.capacity || mainFinancial.capitalCost || mainFinancial.operatingMaintenanceCost)) {
      return {
        capacity: mainFinancial.capacity || "",
        capacityUnit: mainFinancial.capacityUnit || "Tonnes/day",
        capitalCost: mainFinancial.capitalCost || mainFinancial.capitalCostUnit || "", // Backward compatibility
        operatingMaintenanceCost: mainFinancial.operatingMaintenanceCost || mainFinancial.omCost || "" // Backward compatibility
      };
    }

    // Final fallback for completely new entries
    return {
      capacity: "",
      capacityUnit: "Tonnes/day",
      capitalCost: "",
      operatingMaintenanceCost: ""
    };
  };

  // Update financial data for a specific pair
  const updatePairFinancial = (pair: TechActivityPair, field: string, value: string) => {
    const key = getFinancialKey(pair);

    // Get current financial data to preserve other fields
    const currentFinancialData = getFinancialData(pair);

    // Update the specific field
    const updatedFinancialData = {
      ...currentFinancialData,
      [field]: value
    };

    // Update both financialEntries and main financial object for consistency
    updateFormField(`financialEntries.${key}`, field, value);

    // Also update the main financial object if this is the primary technology
    // This ensures backward compatibility and consistency
    if (technologies.length === 1 || pair.technology === technologies[0]) {
      updateFormField('financial', field, value);
    }
  };

  return (
    <>
      {/* Render financial blocks for each tech-activity pair */}
      {techActivityPairs.map((pair) => {
        const pairId = getFinancialKey(pair);
        const financialData = getFinancialData(pair);
        const isOpen = openSections[pairId] !== false; // Default to open
        
        return (
          <Collapsible 
            key={pairId}
            open={isOpen}
            onOpenChange={() => toggleSection(pairId)}
            className="border-2 border-green-200 rounded-md mb-3"
          >
            <CollapsibleTrigger className="flex justify-between items-center w-full px-3 py-2 text-left">
              <div className="font-medium">
                <span>Technology: {pair.technology}</span>
              </div>
              <div>
                {isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </div>
            </CollapsibleTrigger>
            
            <CollapsibleContent className="p-3 pt-0">
              <div className="flex flex-col md:flex-row gap-2 mt-2">
                <div className="flex-1 min-w-0">
                  <label>Capacity</label>
                  <div className="flex gap-2">
                    <Input
                      value={financialData.capacity}
                      onChange={(e) => updatePairFinancial(pair, 'capacity', e.target.value)}
                      placeholder="Enter capacity"
                      className="flex-1"
                      disabled={readOnly}
                    />
                    {readOnly ? (
                      <div className="w-32 border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                        {financialData.capacityUnit || "Tonnes/day"}
                      </div>
                    ) : (
                      <Select
                        value={financialData.capacityUnit || "Tonnes/day"}
                        onValueChange={(value) => updatePairFinancial(pair, 'capacityUnit', value)}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue placeholder="Unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {capacityUnits.map(unit => (
                            <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <label>Capital Cost/ Unit Capacity</label>
                  <Input
                    value={financialData.capitalCost}
                    onChange={(e) => updatePairFinancial(pair, 'capitalCost', e.target.value)}
                    placeholder="Enter cost per unit"
                    disabled={readOnly}
                  />
                </div>

                <div className="flex-1 min-w-0">
                  <label>Annual O&M Cost</label>
                  <Input
                    value={financialData.operatingMaintenanceCost}
                    onChange={(e) => updatePairFinancial(pair, 'operatingMaintenanceCost', e.target.value)}
                    placeholder="Enter Annual O&M cost"
                    disabled={readOnly}
                  />
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        );
      })}

      {/* Show default financial block if no tech-activity pairs exist */}
      {showDefaultFinancial && (
        <div className="border-2 border-green-200 rounded-md mb-3 p-3">
          <div className="font-semibold mb-1">Investment Data</div>
          
          <div className="flex flex-col md:flex-row gap-2">
            <div className="flex-1 min-w-0">
              <label>Capacity</label>
              <div className="flex gap-2">
                <Input
                  value={formData.financial?.capacity || ""}
                  onChange={(e) => updateFormField('financial', 'capacity', e.target.value)}
                  placeholder="Enter capacity"
                  className="flex-1"
                  disabled={readOnly}
                />
                {readOnly ? (
                  <div className="w-32 border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {formData.financial?.capacityUnit || "Tonnes/day"}
                  </div>
                ) : (
                  <Select
                    value={formData.financial?.capacityUnit || "Tonnes/day"}
                    onValueChange={(value) => updateFormField('financial', 'capacityUnit', value)}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Unit" />
                    </SelectTrigger>
                    <SelectContent>
                      {capacityUnits.map(unit => (
                        <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>

            <div className="flex-1 min-w-0">
              <label>Capital Cost/ Unit Capacity</label>
              <Input
                value={formData.financial?.capitalCost || ""}
                onChange={(e) => updateFormField('financial', 'capitalCost', e.target.value)}
                placeholder="Enter cost per unit"
                disabled={readOnly}
              />
            </div>

            <div className="flex-1 min-w-0">
              <label>Annual O&M Cost</label>
              <Input
                value={formData.financial?.operatingMaintenanceCost || ""}
                onChange={(e) => updateFormField('financial', 'operatingMaintenanceCost', e.target.value)}
                placeholder="Enter Annual O&M cost"
                disabled={readOnly}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};
