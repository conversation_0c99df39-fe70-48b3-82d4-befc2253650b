import React, { useState, useEffect, useRef } from 'react';
import { ThumbsUp, ThumbsDown, Trash2, PlusSquare, Send, FileText, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { API_BASE_URL } from '@/utils/endPoints';

interface ChatMessage {
  type: 'user' | 'bot';
  message: string;
  id: string;
}

interface DocumentChatInterfaceProps {
  selectedFile: string | null;
  onContentUpdate?: (content: string) => void;
  documentUuid?: string;
}

const DocumentChatInterface: React.FC<DocumentChatInterfaceProps> = ({ 
  selectedFile,
  onContentUpdate,
  documentUuid
}) => {
  const [chatInput, setChatInput] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  
  // Only scroll to bottom on initial load or when user sends a new message
  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(false);
  
  // Controlled scroll to bottom only when explicitly requested
  useEffect(() => {
    if (shouldScrollToBottom && messagesEndRef.current) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
        setShouldScrollToBottom(false);
      }, 100);
    }
  }, [shouldScrollToBottom, messages]);
  
  // Initial load scroll
  useEffect(() => {
    if (messages.length > 0) {
      setShouldScrollToBottom(true);
    }
  }, []);
  
  const sendQueryToAPI = async (query: string) => {
    if (!documentUuid) return null;
    
    setIsLoading(true);
    try {
      const accessToken = localStorage.getItem('accessToken');
      if (!accessToken) {
        toast({
          title: "Authentication Required",
          description: "Please log in to query documents",
          variant: "destructive",
        });
        return null;
      }

      const encodedQuestion = encodeURIComponent(query);
      const response = await fetch(
        `${API_BASE_URL}/documents/${documentUuid}/query?question=${encodedQuestion}`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to query document');
      }

      const data = await response.json();
      return data.answer;
    } catch (error) {
      console.error('Error querying document:', error);
      toast({
        title: "Error",
        description: "Failed to get response from the document",
        variant: "destructive",
      });
      return "I'm sorry, I couldn't process your question. Please try again later.";
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!chatInput.trim() || isLoading) return;
    
    // Add user message
    const userMessage: ChatMessage = {
      type: 'user',
      message: chatInput,
      id: `user-${Date.now()}`
    };
    
    setMessages(prev => [...prev, userMessage]);
    const userQuery = chatInput;
    setChatInput('');
    
    // Request scroll to bottom when user sends a message
    setShouldScrollToBottom(true);
    
    // Get API response
    const botResponse = await sendQueryToAPI(userQuery);
    
    // Add bot response
    const botMessage: ChatMessage = {
      type: 'bot',
      message: botResponse || "No response received. Please try again later.",
      id: `bot-${Date.now()}`
    };
    
    setMessages(prev => {
      const updatedMessages = [...prev, botMessage];
      
      // Update content if callback provided
      if (onContentUpdate) {
        const allContent = updatedMessages
          .map(msg => `${msg.type === 'user' ? 'User: ' : 'AI: '}${msg.message}`)
          .join('\n\n');
        onContentUpdate(allContent);
      }
      
      // Request scroll to bottom when bot responds
      setShouldScrollToBottom(true);
      
      return updatedMessages;
    });
  };

  const scrollToBottom = () => {
    setShouldScrollToBottom(true);
  };

  return (
    <div className="flex flex-col h-full relative">
      {/* Header */}
      <div className="flex-none border-b p-4 flex items-center justify-between bg-muted/30 dark:bg-background/30">
        <h3 className="text-base font-medium flex items-center">
          <div className="bg-primary/10 p-1.5 rounded-full mr-2">
            <FileText size={16} className="text-primary" />
          </div>
          Chat with Document
        </h3>
        
        {messages.length > 0 && (
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={scrollToBottom}
            className="text-xs"
          >
            Scroll to bottom
          </Button>
        )}
      </div>
      
      {/* Chat messages container with fixed height and scrolling */}
      <div 
        className="flex-1 overflow-y-auto p-4 space-y-4 pb-20" 
        ref={messagesContainerRef}
      >
        {messages.length > 0 ? (
          <>
            {messages.map((msg) => (
              <div key={msg.id} className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-[85%] p-3 rounded-lg ${
                  msg.type === 'user' 
                    ? 'bg-primary text-primary-foreground' 
                    : 'bg-muted/70 dark:bg-background/40 border border-border/40'
                }`}>
                  <p className="text-sm whitespace-pre-wrap">{msg.message}</p>
                  
                  {msg.type === 'bot' && (
                    <div className="mt-2 flex items-center gap-2">
                      <Button variant="ghost" size="icon" className="h-6 w-6 rounded-full hover:bg-background/20">
                        <ThumbsUp className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-6 w-6 rounded-full hover:bg-background/20">
                        <ThumbsDown className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-6 w-6 rounded-full hover:bg-background/20">
                        <PlusSquare className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </>
        ) : (
          // Welcome message
          <div className="h-full flex flex-col items-center justify-center text-center p-6 text-muted-foreground">
            {selectedFile ? (
              <>
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <FileText className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-foreground">Document Ready</h3>
                <p className="mb-6 max-w-sm">
                  Ask questions about the content of the document to analyze it with AI
                </p>
                <Card className="p-4 border border-border/60 w-full max-w-sm bg-background/50">
                  <Button 
                    variant="outline" 
                    className="w-full text-primary border-primary hover:bg-primary/10 mb-2"
                    onClick={() => {
                      setChatInput("What is this document about?");
                      setTimeout(() => {
                        document.querySelector<HTMLFormElement>('form')?.dispatchEvent(
                          new Event('submit', { cancelable: true, bubbles: true })
                        );
                      }, 100);
                    }}
                  >
                    What is this document about?
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full text-primary border-primary hover:bg-primary/10 mb-2"
                    onClick={() => {
                      setChatInput("Summarize the key points");
                      setTimeout(() => {
                        document.querySelector<HTMLFormElement>('form')?.dispatchEvent(
                          new Event('submit', { cancelable: true, bubbles: true })
                        );
                      }, 100);
                    }}
                  >
                    Summarize the key points
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full text-primary border-primary hover:bg-primary/10"
                    onClick={() => {
                      setChatInput("Extract important dates and figures");
                      setTimeout(() => {
                        document.querySelector<HTMLFormElement>('form')?.dispatchEvent(
                          new Event('submit', { cancelable: true, bubbles: true })
                        );
                      }, 100);
                    }}
                  >
                    Extract important dates and figures
                  </Button>
                </Card>
              </>
            ) : (
              <>
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <Upload className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-foreground">No Document Selected</h3>
                <p>
                  Select a document to start chatting
                </p>
              </>
            )}
          </div>
        )}
      </div>

      {/* Input area - Fixed at bottom */}
      <div className="absolute bottom-0 left-0 right-0 border-t p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <form onSubmit={handleSendMessage} className="flex gap-2">
          <Input
            value={chatInput}
            onChange={(e) => setChatInput(e.target.value)}
            placeholder="Ask a question about the document..."
            className="flex-1"
            disabled={!selectedFile || isLoading}
          />
          <Button type="submit" size="icon" disabled={!selectedFile || isLoading}>
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </div>
    </div>
  );
};

export default DocumentChatInterface;
