
import React from 'react';
import { useTheme } from '@/components/ThemeProvider';

interface LogoProps {
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ className }) => {
  const { theme } = useTheme();

  return (
    <a
      href="https://www.recre8.earth"
      target="_blank"
      rel="noopener noreferrer"
      className={`flex items-center ${className}`}
    >
      <img
        src={theme === 'dark' ? '/logo-light.svg' : '/logo-dark.svg'}
        alt="Recre8"
        className="h-8 md:h-10 w-auto"
      />
    </a>
  );
};

export default Logo;
