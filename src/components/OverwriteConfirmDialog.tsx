import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

interface OverwriteConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  flowName: string;
  flowType: 'inventory' | 'scenario';
  onConfirm: () => void;
  onCancel: () => void;
}

export const OverwriteConfirmDialog: React.FC<OverwriteConfirmDialogProps> = ({
  open,
  onOpenChange,
  flowName,
  flowType,
  onConfirm,
  onCancel,
}) => {
  const handleConfirm = () => {
    onConfirm();
    onOpenChange(false);
  };

  const handleCancel = () => {
    onCancel();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]" data-testid="overwrite-dialog">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            Overwrite Existing {flowType === 'inventory' ? 'Inventory' : 'Scenario'}?
          </DialogTitle>
          <DialogDescription>
            {flowType === 'inventory' ? 'An' : 'A'} {flowType} with the name "{flowName}" already exists.
            Do you want to overwrite it with the current data?
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
            <div className="text-sm text-amber-800">
              <strong>Warning:</strong> This action cannot be undone. The existing {flowType} will be permanently replaced with your current work.
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            data-testid="cancel-overwrite-button"
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            data-testid="confirm-overwrite-button"
          >
            Overwrite
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
