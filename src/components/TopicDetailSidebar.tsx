
import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ArrowLeft } from 'lucide-react';
import { TopicDetailData } from '@/types/TopicDetail';

interface TopicDetailSidebarProps {
  topics: TopicDetailData[];
  currentTab: string;
  isLoading?: boolean;
}

const TopicDetailSidebar: React.FC<TopicDetailSidebarProps> = ({
  topics,
  currentTab,
  isLoading
}) => {
  const navigate = useNavigate();
  const { tileId } = useParams();

  const handleTopicClick = (topic: TopicDetailData, index: number) => {
    // Use consistent ID generation: topic.id or (index + 1) to match other components
    const topicId = topic.id?.toString() || (index + 1).toString();
    navigate(`/dashboard/${currentTab}/${topicId}`, {
      state: { topic: { ...topic, id: topicId } }
    });
  };

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  if (isLoading) {
    return (
      <div className="w-80 border-r bg-background p-4">
        <Button 
          onClick={handleBackToDashboard}
          variant="ghost" 
          className="mb-4 w-full justify-start"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Dashboard
        </Button>
        
        <div className="space-y-2">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-16 bg-muted animate-pulse rounded-md" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 border-r bg-background flex flex-col">
      <div className="p-4 border-b">
        <Button 
          onClick={handleBackToDashboard}
          variant="ghost" 
          className="w-full justify-start"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Dashboard
        </Button>
      </div>
      
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-2">
          {topics.map((topic, index) => {
            // Use consistent ID generation: topic.id or (index + 1) to match other components
            const topicId = topic.id?.toString() || (index + 1).toString();
            const isActive = tileId === topicId;

            return (
              <Button
                key={topicId}
                onClick={() => handleTopicClick(topic, index)}
                variant={isActive ? "default" : "ghost"}
                className={`w-full justify-start h-auto p-3 text-left ${
                  isActive ? "bg-recrea-turquoise text-white" : ""
                }`}
              >
                <div className="space-y-1">
                  <div className="font-medium text-sm">{topic.title}</div>
                  {topic.description && (
                    <div className="text-xs opacity-70 line-clamp-2">
                      {topic.description.substring(0, 80)}...
                    </div>
                  )}
                </div>
              </Button>
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );
};

export default TopicDetailSidebar;
