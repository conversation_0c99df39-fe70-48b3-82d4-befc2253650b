import React from 'react';
import { EdgeProps, getSmooth<PERSON>tep<PERSON><PERSON>, EdgeLabelRenderer, BaseEdge } from '@xyflow/react';

/**
 * Custom edge component that handles multiple edges between same nodes
 * with proper spacing and visual differentiation
 */
export const MultiEdge: React.FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  markerEnd,
  label,
  labelStyle,
  selected
}) => {
  // Get multi-edge data
  const multiEdgeOffset = data?.multiEdgeOffset || 0;
  const multiEdgeTotal = data?.multiEdgeTotal || 1;
  const multiEdgeIndex = data?.multiEdgeIndex || 0;

  if (multiEdgeTotal > 1) {
    console.log(`🎨 Rendering multi-edge ${multiEdgeIndex + 1}/${multiEdgeTotal}:`, {
      id,
      label,
      offset: multiEdgeOffset,
      sourceX,
      sourceY,
      targetX,
      targetY
    });
  }
  
  // Calculate offset positions based on edge direction
  // For horizontal edges, offset vertically; for vertical edges, offset horizontally
  const isHorizontal = Math.abs(targetX - sourceX) > Math.abs(targetY - sourceY);

  let adjustedSourceX = sourceX;
  let adjustedSourceY = sourceY;
  let adjustedTargetX = targetX;
  let adjustedTargetY = targetY;

  if (isHorizontal) {
    // Horizontal edge - offset vertically
    adjustedSourceY = sourceY + multiEdgeOffset;
    adjustedTargetY = targetY + multiEdgeOffset;
  } else {
    // Vertical edge - offset horizontally
    adjustedSourceX = sourceX + multiEdgeOffset;
    adjustedTargetX = targetX + multiEdgeOffset;
  }
  
  // Calculate path with enhanced curvature for multiple edges
  const curvature = multiEdgeTotal > 1 ? 0.3 + (multiEdgeIndex * 0.1) : 0.25;
  
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX: adjustedSourceX,
    sourceY: adjustedSourceY,
    sourcePosition,
    targetX: adjustedTargetX,
    targetY: adjustedTargetY,
    targetPosition,
    borderRadius: 8,
    offset: multiEdgeOffset
  });
  
  // Enhanced styling for multiple edges
  const edgeStyle = {
    ...style,
    // Add subtle animation for multiple edges
    ...(multiEdgeTotal > 1 && {
      strokeDasharray: multiEdgeIndex % 2 === 1 ? '5,5' : 'none',
      animation: data?.outputType?.includes('byproduct') ? 'dash 2s linear infinite' : 'none'
    })
  };
  
  return (
    <>
      <BaseEdge
        id={id}
        path={edgePath}
        style={edgeStyle}
        markerEnd={markerEnd}
      />
      {label && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              ...labelStyle,
              pointerEvents: 'all',
              // Slightly offset labels for multiple edges to prevent overlap
              marginTop: multiEdgeOffset * 0.3,
              zIndex: 1000 + multiEdgeIndex
            }}
            className="nodrag nopan"
          >
            {label}
          </div>
        </EdgeLabelRenderer>
      )}
    </>
  );
};

/**
 * Enhanced smooth step edge with better multiple edge support
 */
export const EnhancedSmoothStepEdge: React.FC<EdgeProps> = (props) => {
  const { data } = props;
  
  // Use custom multi-edge component if this edge is part of multiple edges
  if (data?.multiEdgeTotal > 1) {
    return <MultiEdge {...props} />;
  }
  
  // Use default smooth step path for single edges
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX: props.sourceX,
    sourceY: props.sourceY,
    sourcePosition: props.sourcePosition,
    targetX: props.targetX,
    targetY: props.targetY,
    targetPosition: props.targetPosition,
    borderRadius: 8
  });
  
  return (
    <>
      <BaseEdge
        id={props.id}
        path={edgePath}
        style={props.style}
        markerEnd={props.markerEnd}
      />
      {props.label && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              ...props.labelStyle,
              pointerEvents: 'all'
            }}
            className="nodrag nopan"
          >
            {props.label}
          </div>
        </EdgeLabelRenderer>
      )}
    </>
  );
};

// Export edge types for React Flow
export const customEdgeTypes = {
  'smoothstep': EnhancedSmoothStepEdge,
  'multi-edge': MultiEdge
};
