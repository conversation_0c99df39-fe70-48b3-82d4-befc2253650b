import { useToast } from "@/hooks/use-toast";
import { API_BASE_URL } from '@/utils/endPoints';

interface ToastUtils {
  toast: ReturnType<typeof useToast>["toast"];
  dismiss: ReturnType<typeof useToast>["dismiss"];
  toasts: ReturnType<typeof useToast>["toasts"];
}

// Activity interfaces
export interface Activity {
  uuid: string;
  name: string;
  description?: string;
  sector_uuid: string;
  created_at: string;
  updated_at?: string;
}

export interface ActivityCreate {
  name: string;
  description?: string;
  sector_uuid: string;
}

export interface ActivityResponse {
  uuid: string;
  name: string;
  description?: string;
  sector_uuid: string;
  created_at: string;
  updated_at?: string;
}



// Helper function to get auth headers
const getAuthHeaders = () => {
  const accessToken = localStorage.getItem('accessToken');
  if (!accessToken) {
    throw new Error('No access token found. Please log in again.');
  }
  
  return {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  };
};

/**
 * Fetch activities for a specific sector
 */
export const fetchActivities = async (
  sectorUuid: string,
  toastUtils?: ToastUtils
): Promise<ActivityResponse[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/sectors/${sectorUuid}/activities`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch activities: ${response.status} ${response.statusText}`);
    }

    const activities: ActivityResponse[] = await response.json();
    return activities;
  } catch (error) {
    console.error('Error fetching activities:', error);
    
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Loading Activities",
        description: error instanceof Error ? error.message : "Failed to load activities from server",
        variant: "destructive"
      });
    }
    
    // Return empty array as fallback
    return [];
  }
};

/**
 * Create a new activity
 */
export const createActivity = async (
  activityData: ActivityCreate,
  toastUtils?: ToastUtils
): Promise<ActivityResponse | null> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/activities`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(activityData)
    });

    if (!response.ok) {
      throw new Error(`Failed to create activity: ${response.status} ${response.statusText}`);
    }

    const newActivity: ActivityResponse = await response.json();

    if (toastUtils) {
      toastUtils.toast({
        title: "Activity Created",
        description: `Successfully created "${newActivity.name}"`,
        variant: "default"
      });
    }

    return newActivity;
  } catch (error) {
    console.error('Error creating activity:', error);

    if (toastUtils) {
      toastUtils.toast({
        title: "Error Creating Activity",
        description: error instanceof Error ? error.message : "Failed to create activity",
        variant: "destructive"
      });
    }

    return null;
  }
};

/**
 * Associate an activity with a sector
 */
export const associateActivityWithSector = async (
  sectorUuid: string,
  activityUuid: string,
  toastUtils?: ToastUtils
): Promise<boolean> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/sectors/${sectorUuid}/activities/${activityUuid}`, {
      method: 'POST',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to associate activity with sector: ${response.status} ${response.statusText}`);
    }

    if (toastUtils) {
      toastUtils.toast({
        title: "Activity Associated",
        description: "Successfully associated activity with sector",
        variant: "default"
      });
    }

    return true;
  } catch (error) {
    console.error('Error associating activity with sector:', error);

    if (toastUtils) {
      toastUtils.toast({
        title: "Error Associating Activity",
        description: error instanceof Error ? error.message : "Failed to associate activity with sector",
        variant: "destructive"
      });
    }

    return false;
  }
};

/**
 * Helper function to get sector UUID from industry ID
 * This function attempts to map industry IDs to actual sector UUIDs
 */
export const getSectorUuidFromIndustryId = async (industryId: string): Promise<string> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      console.warn('No access token found, using default sector UUID');
      return 'default-sector-uuid';
    }

    // Fetch sectors from API
    const response = await fetch(API_BASE_URL + '/sectors', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch sectors');
    }

    const sectors = await response.json();

    // Try to find a matching sector based on industry ID
    const normalizedIndustryId = industryId.toLowerCase().replace(/[-_]/g, ' ');

    const matchingSector = sectors.find((sector: any) => {
      const normalizedSectorName = sector.name.toLowerCase().replace(/[-_]/g, ' ');
      return normalizedSectorName.includes(normalizedIndustryId) ||
             normalizedIndustryId.includes(normalizedSectorName);
    });

    if (matchingSector) {
      return matchingSector.uuid;
    }

    // If no match found, return the first sector or default
    return sectors.length > 0 ? sectors[0].uuid : 'default-sector-uuid';

  } catch (error) {
    console.error('Error fetching sector UUID:', error);
    return 'default-sector-uuid';
  }
};

/**
 * Synchronous version that uses a simple mapping
 * Use this when you can't use async/await
 */
export const getSectorUuidFromIndustryIdSync = (industryId: string): string => {
  // Simple mapping based on common industry patterns
  const industryToSectorMap: Record<string, string> = {
    'oil-gas': 'oil-gas-sector-uuid',
    'oil-and-gas': 'oil-gas-sector-uuid',
    'manufacturing': 'manufacturing-sector-uuid',
    'energy': 'energy-sector-uuid',
    'chemicals': 'chemicals-sector-uuid',
    'mining': 'mining-sector-uuid',
    'automotive': 'automotive-sector-uuid',
    'steel': 'steel-sector-uuid',
    'cement': 'cement-sector-uuid',
    // Add more mappings as needed
  };

  return industryToSectorMap[industryId] || 'default-sector-uuid';
};

/**
 * Generate numbered activity name if duplicate exists in existing nodes
 */
export const generateNumberedActivityName = (
  baseName: string,
  existingNodeNames: string[]
): string => {
  // Filter out undefined, null, or empty values and convert to lowercase
  const existingNames = existingNodeNames
    .filter(name => name && typeof name === 'string' && name.trim() !== '')
    .map(name => name.toLowerCase());

  const baseNameLower = baseName.toLowerCase();

  // Count how many nodes already have this base name (with or without numbers)
  let count = 0;
  const pattern = new RegExp(`^${baseNameLower}(\\s+(\\d+))?$`);

  existingNames.forEach(name => {
    if (pattern.test(name)) {
      count++;
    }
  });

  // If no existing nodes with this name, return original name
  if (count === 0) {
    return baseName;
  }

  // If there are existing nodes, the next one should be numbered
  // First duplicate becomes "Name 2", second becomes "Name 3", etc.
  return `${baseName} ${count + 1}`;
};
