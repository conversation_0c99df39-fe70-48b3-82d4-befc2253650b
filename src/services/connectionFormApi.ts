
import { TechnologyResponse, MaterialResponse, EnergyResponse, EmissionResponse } from '@/components/ConnectionForm/types';
import { ActivityResponse } from '@/services/activitiesApi';
import { API_BASE_URL } from '@/utils/endPoints';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const accessToken = localStorage.getItem('accessToken');
  if (!accessToken) {
    throw new Error('No access token found. Please log in again.');
  }
  
  return {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  };
};

// Updated interface for toast utilities to match useToast hook
interface ToastUtils {
  toast: (options: {
    title: string;
    description: string;
    variant: "default" | "destructive";
  }) => void;
}

/**
 * Helper function to strip numbers from activity names
 * e.g., "Gas Oil Separation 2" -> "Gas Oil Separation"
 */
export const stripNumbersFromActivityName = (activityName: string): string => {
  if (!activityName) return activityName;

  // Remove trailing numbers pattern like " 2", " 3", etc.
  return activityName.replace(/\s+\d+$/, '').trim();
};

/**
 * Fetch activities for a specific sector and find activity UUID by name
 * This function now handles numbered activity names by stripping the numbers
 */
export const findActivityUuidByName = async (
  sectorUuid: string,
  activityName: string,
  toastUtils?: ToastUtils
): Promise<string | null> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/sectors/${sectorUuid}/activities`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch activities: ${response.status} ${response.statusText}`);
    }

    const activities: ActivityResponse[] = await response.json();

    // First try to find exact match
    let foundActivity = activities.find(activity => activity.name === activityName);

    // If no exact match and the name has numbers, try with stripped name
    if (!foundActivity && activityName !== stripNumbersFromActivityName(activityName)) {
      const strippedName = stripNumbersFromActivityName(activityName);
      foundActivity = activities.find(activity => activity.name === strippedName);

      if (foundActivity) {
        console.log(`Found activity UUID by stripping numbers: "${activityName}" -> "${strippedName}" -> ${foundActivity.uuid}`);
      }
    }

    return foundActivity ? foundActivity.uuid : null;
  } catch (error) {
    console.error('Error finding activity UUID by name:', error);

    if (toastUtils) {
      toastUtils.toast({
        title: "Error Finding Activity",
        description: error instanceof Error ? error.message : "Failed to find activity",
        variant: "destructive"
      });
    }

    return null;
  }
};

/**
 * Fetch technologies for a specific activity
 */
export const fetchTechnologies = async (
  activityUuid: string,
  toastUtils?: ToastUtils
): Promise<TechnologyResponse[]> => {
  console.log('fetchTechnologies: Called with activityUuid:', activityUuid);

  try {
    const url = `${API_BASE_URL}/resources/activities/${activityUuid}/technologies`;
    console.log('fetchTechnologies: Making request to:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      console.error('fetchTechnologies: Request failed with status:', response.status, response.statusText);
      throw new Error(`Failed to fetch technologies: ${response.status} ${response.statusText}`);
    }

    const technologies: TechnologyResponse[] = await response.json();
    console.log('fetchTechnologies: Successfully fetched technologies:', technologies);
    return technologies;
  } catch (error) {
    console.error('Error fetching technologies:', error);
    
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Loading Technologies",
        description: error instanceof Error ? error.message : "Failed to load technologies from server",
        variant: "destructive"
      });
    }
    
    // Return empty array as fallback
    return [];
  }
};

/**
 * Fetch technologies for a specific activity by name (with number stripping support)
 * This function now uses UUID-based fetching for better reliability
 */
export const fetchActivityNameTechnologies = async (
  activityName: string,
  toastUtils?: ToastUtils,
  sectorUuid?: string
): Promise<TechnologyResponse[]> => {
  console.log('fetchActivityNameTechnologies: Called with activityName:', activityName);

  try {
    // If we have sectorUuid, try to get the activity UUID first and use UUID-based fetching
    if (sectorUuid) {
      console.log('fetchActivityNameTechnologies: Attempting UUID-based fetching with sectorUuid:', sectorUuid);

      const activityUuid = await findActivityUuidByName(sectorUuid, activityName, toastUtils);
      if (activityUuid) {
        console.log('fetchActivityNameTechnologies: Found activity UUID, using UUID-based fetching:', activityUuid);
        return await fetchTechnologies(activityUuid, toastUtils);
      }
    }

    // Fallback to name-based fetching
    console.log('fetchActivityNameTechnologies: Falling back to name-based fetching');

    // Try with original name first
    let url = `${API_BASE_URL}/resources/activities/name/${activityName}/technologies`;
    console.log('fetchActivityNameTechnologies: Making request to:', url);

    let response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    // If original name fails and it has numbers, try with stripped name
    if (!response.ok && activityName !== stripNumbersFromActivityName(activityName)) {
      const strippedName = stripNumbersFromActivityName(activityName);
      console.log('fetchActivityNameTechnologies: Original name failed, trying with stripped name:', strippedName);

      url = `${API_BASE_URL}/resources/activities/name/${strippedName}/technologies`;
      console.log('fetchActivityNameTechnologies: Making request to:', url);

      response = await fetch(url, {
        method: 'GET',
        headers: getAuthHeaders()
      });
    }

    if (!response.ok) {
      console.error('fetchActivityNameTechnologies: Request failed with status:', response.status, response.statusText);
      throw new Error(`Failed to fetch technologies: ${response.status} ${response.statusText}`);
    }

    const technologies: TechnologyResponse[] = await response.json();
    console.log('fetchActivityNameTechnologies: Successfully fetched technologies:', technologies);
    return technologies;
  } catch (error) {
    console.error('Error fetching technologies:', error);

    if (toastUtils) {
      toastUtils.toast({
        title: "Error Loading Technologies",
        description: error instanceof Error ? error.message : "Failed to load technologies from server",
        variant: "destructive"
      });
    }

    // Return empty array as fallback
    return [];
  }
};

/**
 * Create a new technology
 */
export const createTechnology = async (
  technologyData: { name: string; description?: string },
  toastUtils?: ToastUtils
): Promise<TechnologyResponse | null> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/technologies`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(technologyData)
    });

    if (!response.ok) {
      throw new Error(`Failed to create technology: ${response.status} ${response.statusText}`);
    }

    const newTechnology: TechnologyResponse = await response.json();

    if (toastUtils) {
      toastUtils.toast({
        title: "Technology Created",
        description: `Successfully created "${newTechnology.name}"`,
        variant: "default"
      });
    }

    return newTechnology;
  } catch (error) {
    console.error('Error creating technology:', error);
    
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Creating Technology",
        description: error instanceof Error ? error.message : "Failed to create technology",
        variant: "destructive"
      });
    }
    
    return null;
  }
};

/**
 * Associate a technology with an activity
 */
export const associateTechnologyWithActivity = async (
  activityUuid: string,
  technologyUuid: string,
  toastUtils?: ToastUtils
): Promise<boolean> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/activities/${activityUuid}/technologies/${technologyUuid}`, {
      method: 'POST',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to associate technology with activity: ${response.status} ${response.statusText}`);
    }

    if (toastUtils) {
      toastUtils.toast({
        title: "Technology Associated",
        description: "Successfully associated technology with activity",
        variant: "default"
      });
    }

    return true;
  } catch (error) {
    console.error('Error associating technology with activity:', error);
    
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Associating Technology",
        description: error instanceof Error ? error.message : "Failed to associate technology with activity",
        variant: "destructive"
      });
    }
    
    return false;
  }
};

/**
 * Fetch materials for a specific sector
 */
export const fetchMaterials = async (
  sectorUuid: string,
  toastUtils?: ToastUtils
): Promise<MaterialResponse[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/sectors/${sectorUuid}/materials`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch materials: ${response.status} ${response.statusText}`);
    }

    const materials: MaterialResponse[] = await response.json();
    return materials;
  } catch (error) {
    console.error('Error fetching materials:', error);
    
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Loading Materials",
        description: error instanceof Error ? error.message : "Failed to load materials from server",
        variant: "destructive"
      });
    }
    
    // Return empty array as fallback
    return [];
  }
};

/**
 * Fetch all energies
 */
export const fetchEnergies = async (
  toastUtils?: ToastUtils
): Promise<EnergyResponse[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/energies`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch energies: ${response.status} ${response.statusText}`);
    }

    const energies: EnergyResponse[] = await response.json();
    return energies;
  } catch (error) {
    console.error('Error fetching energies:', error);
    
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Loading Energies",
        description: error instanceof Error ? error.message : "Failed to load energies from server",
        variant: "destructive"
      });
    }
    
    // Return empty array as fallback
    return [];
  }
};

/**
 * Fetch all emissions
 */
export const fetchEmissions = async (
  toastUtils?: ToastUtils
): Promise<EmissionResponse[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/emissions`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch emissions: ${response.status} ${response.statusText}`);
    }

    const emissions: EmissionResponse[] = await response.json();
    return emissions;
  } catch (error) {
    console.error('Error fetching emissions:', error);

    if (toastUtils) {
      toastUtils.toast({
        title: "Error Loading Emissions",
        description: error instanceof Error ? error.message : "Failed to load emissions from server",
        variant: "destructive"
      });
    }

    // Return empty array as fallback
    return [];
  }
};

/**
 * Create a new material
 */
export const createMaterial = async (
  materialData: { name: string },
  toastUtils?: ToastUtils
): Promise<MaterialResponse | null> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/materials`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(materialData)
    });

    if (!response.ok) {
      throw new Error(`Failed to create material: ${response.status} ${response.statusText}`);
    }

    const newMaterial: MaterialResponse = await response.json();

    if (toastUtils) {
      toastUtils.toast({
        title: "Material Created",
        description: `Successfully created "${newMaterial.name}"`,
        variant: "default"
      });
    }

    return newMaterial;
  } catch (error) {
    console.error('Error creating material:', error);

    if (toastUtils) {
      toastUtils.toast({
        title: "Error Creating Material",
        description: error instanceof Error ? error.message : "Failed to create material",
        variant: "destructive"
      });
    }

    return null;
  }
};

/**
 * Associate material with sector
 */
export const associateMaterialWithSector = async (
  sectorUuid: string,
  materialUuid: string,
  toastUtils?: ToastUtils
): Promise<boolean> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/sectors/${sectorUuid}/materials/${materialUuid}`, {
      method: 'POST',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to associate material with sector: ${response.status} ${response.statusText}`);
    }

    return true;
  } catch (error) {
    console.error('Error associating material with sector:', error);

    if (toastUtils) {
      toastUtils.toast({
        title: "Error Associating Material",
        description: error instanceof Error ? error.message : "Failed to associate material with sector",
        variant: "destructive"
      });
    }

    return false;
  }
};

/**
 * Create a new energy
 */
export const createEnergy = async (
  energyData: { name: string },
  toastUtils?: ToastUtils
): Promise<EnergyResponse | null> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/energies`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(energyData)
    });

    if (!response.ok) {
      throw new Error(`Failed to create energy: ${response.status} ${response.statusText}`);
    }

    const newEnergy: EnergyResponse = await response.json();

    if (toastUtils) {
      toastUtils.toast({
        title: "Energy Created",
        description: `Successfully created "${newEnergy.name}"`,
        variant: "default"
      });
    }

    return newEnergy;
  } catch (error) {
    console.error('Error creating energy:', error);

    if (toastUtils) {
      toastUtils.toast({
        title: "Error Creating Energy",
        description: error instanceof Error ? error.message : "Failed to create energy",
        variant: "destructive"
      });
    }

    return null;
  }
};

/**
 * Associate energy with sector
 */
export const associateEnergyWithSector = async (
  sectorUuid: string,
  energyUuid: string,
  toastUtils?: ToastUtils
): Promise<boolean> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/sectors/${sectorUuid}/energies/${energyUuid}`, {
      method: 'POST',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to associate energy with sector: ${response.status} ${response.statusText}`);
    }

    return true;
  } catch (error) {
    console.error('Error associating energy with sector:', error);

    if (toastUtils) {
      toastUtils.toast({
        title: "Error Associating Energy",
        description: error instanceof Error ? error.message : "Failed to associate energy with sector",
        variant: "destructive"
      });
    }

    return false;
  }
};

/**
 * Create a new emission
 */
export const createEmission = async (
  emissionData: { name: string },
  toastUtils?: ToastUtils
): Promise<EmissionResponse | null> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/emissions`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(emissionData)
    });

    if (!response.ok) {
      throw new Error(`Failed to create emission: ${response.status} ${response.statusText}`);
    }

    const newEmission: EmissionResponse = await response.json();

    if (toastUtils) {
      toastUtils.toast({
        title: "Emission Created",
        description: `Successfully created "${newEmission.name}"`,
        variant: "default"
      });
    }

    return newEmission;
  } catch (error) {
    console.error('Error creating emission:', error);

    if (toastUtils) {
      toastUtils.toast({
        title: "Error Creating Emission",
        description: error instanceof Error ? error.message : "Failed to create emission",
        variant: "destructive"
      });
    }

    return null;
  }
};
