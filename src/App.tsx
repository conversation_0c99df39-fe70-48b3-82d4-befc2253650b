import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Index from '@/pages/Index';
import Dashboard from '@/pages/Dashboard';
import DocumentChat from '@/pages/DocumentChat';
import QuerySearch from '@/pages/QuerySearch';
import Login from '@/pages/Login';
import SignUp from '@/pages/SignUp';
import EmailVerification from '@/pages/EmailVerification';
import Profile from '@/pages/Profile';
import DomainCorpus from '@/pages/DomainCorpus';
import Assessment from '@/pages/Assessment';
import ClimateRiskAssessment from '@/pages/ClimateRiskAssessment';
import NotFound from '@/pages/NotFound';
import TopicDetail from '@/pages/TopicDetail';
import { ThemeProvider } from '@/components/ThemeProvider';
import { ToastProvider } from '@/contexts/ToastContext';
import { Toaster } from '@/components/ui/toaster';
import NotesCreator from '@/pages/NotesCreator';
import Optimizer from '@/pages/Optimizer';
import IndustryFlow from '@/pages/IndustryFlow';
import Layout from '@/components/Layout';
import Marketplace from '@/pages/Marketplace';
import ActivityDetails from '@/pages/ActivityDetails';
import TechDetails from '@/pages/TechDetails';
import { SectorActivitiesProvider } from '@/contexts/SectorActivitiesContext';

// Shared layout routes
function LayoutRoute({ element }: { element: React.ReactNode }) {
  return (
    <Layout>
      {element}
    </Layout>
  );
}

function App() {
  return (
    <div className="App">
      <ThemeProvider>
        <ToastProvider>
          <Toaster />
          <BrowserRouter>
            <Routes>
              {/* Public routes - no authentication required, no SectorActivitiesProvider needed */}
              <Route path="/" element={<Index />} />
              <Route path="/home" element={<Index />} />
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<SignUp />} />
              <Route path="/verify-email" element={<EmailVerification />} />
              <Route path="*" element={<NotFound />} />

              {/* Protected routes with shared layout and SectorActivitiesProvider */}
              <Route path="/dashboard" element={
                <SectorActivitiesProvider>
                  <Layout><Dashboard /></Layout>
                </SectorActivitiesProvider>
              } />
              <Route path="/dashboard/:tab/:tileId" element={
                <SectorActivitiesProvider>
                  <TopicDetail />
                </SectorActivitiesProvider>
              } />
              <Route path="/document-chat" element={
                <SectorActivitiesProvider>
                  <Layout><DocumentChat /></Layout>
                </SectorActivitiesProvider>
              } />
              <Route path="/query-search" element={
                <SectorActivitiesProvider>
                  <Layout><QuerySearch /></Layout>
                </SectorActivitiesProvider>
              } />
              <Route path="/notes-creator" element={
                <SectorActivitiesProvider>
                  <Layout><NotesCreator /></Layout>
                </SectorActivitiesProvider>
              } />
              <Route path="/optimizer" element={
                <SectorActivitiesProvider>
                  <Layout><Optimizer /></Layout>
                </SectorActivitiesProvider>
              } />
              <Route path="/industry-flow/:industryId" element={
                <SectorActivitiesProvider>
                  <Layout><IndustryFlow /></Layout>
                </SectorActivitiesProvider>
              } />
              <Route path="/profile" element={
                <SectorActivitiesProvider>
                  <Layout><Profile /></Layout>
                </SectorActivitiesProvider>
              } />
              <Route path="/domain-corpus" element={
                <SectorActivitiesProvider>
                  <Layout><DomainCorpus /></Layout>
                </SectorActivitiesProvider>
              } />
              <Route path="/assessment" element={
                <SectorActivitiesProvider>
                  <Layout><Assessment /></Layout>
                </SectorActivitiesProvider>
              } />
              <Route path="/climate-risk-assessment" element={
                <SectorActivitiesProvider>
                  <Layout><ClimateRiskAssessment /></Layout>
                </SectorActivitiesProvider>
              } />
              <Route path="/marketplace" element={
                <SectorActivitiesProvider>
                  <Layout><Marketplace /></Layout>
                </SectorActivitiesProvider>
              } />
              <Route path="/marketplace/:activityName" element={
                <SectorActivitiesProvider>
                  <Layout><ActivityDetails /></Layout>
                </SectorActivitiesProvider>
              } />
              <Route path="/marketplace/:activityId/:techSlug" element={
                <SectorActivitiesProvider>
                  <Layout><TechDetails /></Layout>
                </SectorActivitiesProvider>
              } />
            </Routes>
          </BrowserRouter>
        </ToastProvider>
      </ThemeProvider>
    </div>
  );
}

export default App;
