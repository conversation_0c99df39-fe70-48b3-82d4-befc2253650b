
import { useState, useEffect, useCallback } from 'react';
import { useToast } from './use-toast';
import { useAccessToken } from './useAccessToken';
import { API_BASE_URL } from '@/utils/endPoints';

export interface Sector {
  uuid: string;
  name: string;
  default_flow: string | null;
}

// Module-level cache
// let cachedSectors: Sector[] | null = null;

export const useSectors = (fetchOnMount = true) => {
  const [sectors, setSectors] = useState<Sector[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const accessToken = useAccessToken();

  const fetchSectorsNow = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      if (!accessToken) {
        setError('No access token found. Please log in again.');
        setIsLoading(false);
        return;
      }
      const response = await fetch(API_BASE_URL + '/sectors', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error('Failed to fetch sectors');
      }
      const data = await response.json();
      // cachedSectors = data;
      setSectors(data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch sectors');
      toast({
        title: 'Error fetching sectors',
        description: err.message || 'Please try again later',
        variant: 'destructive'
      });
      setSectors([]);
    } finally {
      setIsLoading(false);
    }
  }, [accessToken, toast]);

  useEffect(() => {
    if (fetchOnMount) {
      fetchSectorsNow();
    }
  }, [fetchOnMount, fetchSectorsNow]);

  return { sectors, isLoading, error, fetchSectorsNow };
};
