
import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { ChatMessage, TopicChatState } from '@/types/TopicDetail';
import { fetchTopicChatResponse } from '@/services/topicDetailApi';
import { dashboardCache } from '@/utils/dashboardCache';

export const useTopicDetail = (topicId: string, topicTitle: string, sector: string, tab?: string) => {
  const { toast, dismiss, toasts } = useToast();
  const [isLoadingChat, setIsLoadingChat] = useState(false);
  const [chatState, setChatState] = useState<TopicChatState>({
    messages: [],
    summary: {
      content: '',
      isLoading: true,
      error: null
    }
  });

  // Reset chat state and load cached messages when topic changes
  useEffect(() => {
    // Reset chat state first
    setChatState({
      messages: [],
      summary: {
        content: '',
        isLoading: true,
        error: null
      }
    });

    // Then load cached messages for the new topic
    const cachedMessages = dashboardCache.getChatHistory(topicId, tab || 'default', sector);
    if (cachedMessages.length > 0) {
      setChatState(prev => ({
        ...prev,
        messages: cachedMessages
      }));
    }

    // Load summary for the new topic
    loadTopicSummary();
  }, [topicId, tab, sector]); // Dependencies that trigger chat reset

  const loadTopicSummary = async () => {
    // Check cache first
    const cachedResponse = dashboardCache.getCachedResponse(topicId, tab || 'default', sector);
    if (cachedResponse) {
      setChatState(prev => ({
        ...prev,
        summary: {
          content: cachedResponse,
          isLoading: false,
          error: null
        }
      }));
      return;
    }

    setChatState(prev => ({
      ...prev,
      summary: {
        ...prev.summary,
        isLoading: true,
        error: null
      }
    }));

    try {
      const response = await fetchTopicChatResponse(
        `Provide a comprehensive summary of ${topicTitle} in the context of ${sector} sector.`,
        sector,
        { toast, dismiss, toasts }
      );

      if (response) {
        // Cache the response
        dashboardCache.setCachedResponse(topicId, tab || 'default', sector, response);

        setChatState(prev => ({
          ...prev,
          summary: {
            content: response,
            isLoading: false,
            error: null
          }
        }));
      } else {
        throw new Error('No response received');
      }
    } catch (error) {
      // console.error('Error loading topic summary:', error);
      setChatState(prev => ({
        ...prev,
        summary: {
          content: 'Failed to load summary. Please try again later.',
          isLoading: false,
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        }
      }));
    }
  };

  const sendMessage = async (message: string) => {
    if (!message.trim() || isLoadingChat || !topicId) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: message.trim(),
      isUser: true,
      timestamp: Date.now()
    };

    // Update local state and cache with user message
    const updatedMessages = [...chatState.messages, userMessage];
    setChatState(prev => ({
      ...prev,
      messages: updatedMessages
    }));
    dashboardCache.setChatHistory(topicId, tab || 'default', sector, updatedMessages);

    setIsLoadingChat(true);

    try {
      const response = await fetchTopicChatResponse(message.trim(), sector, { toast, dismiss, toasts });
      
      if (response) {
        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          content: response,
          isUser: false,
          timestamp: Date.now()
        };

        // Update local state and cache with assistant message
        const finalMessages = [...updatedMessages, assistantMessage];
        setChatState(prev => ({
          ...prev,
          messages: finalMessages
        }));
        dashboardCache.setChatHistory(topicId, tab || 'default', sector, finalMessages);
      } else {
        throw new Error('No response received');
      }
    } catch (error: any) {
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: 'Sorry, I encountered an error while processing your question. Please try again.',
        isUser: false,
        timestamp: Date.now()
      };

      // Update local state and cache with error message
      const finalMessages = [...updatedMessages, errorMessage];
      setChatState(prev => ({
        ...prev,
        messages: finalMessages
      }));
      dashboardCache.setChatHistory(topicId, tab || 'default', sector, finalMessages);
    } finally {
      setIsLoadingChat(false);
    }
  };

  return {
    chatState,
    isLoadingChat,
    sendMessage,
    reloadSummary: () => loadTopicSummary()
  };
};
